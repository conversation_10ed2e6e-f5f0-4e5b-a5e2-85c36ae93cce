# Dependencies
node_modules/
jspm_packages/

# Package manager logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage and test files
coverage/
*.lcov
.nyc_output
test-results/

# Build outputs
dist/
build/
.next/
out/
.nuxt/
.cache/
.parcel-cache/
.storybook-out/
.out/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Package manager files
.npm
.yarn-integrity
*.tgz
.pnp
.pnp.js

# Environment variables
.env
.env*.local
.env.test
.env.production
.env.development

# Logs
logs/
*.log

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database files
*.sqlite
*.sqlite3
*.db

# Upload directories
uploads/
temp-uploads/
public/uploads/

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Docker
.dockerignore

# Process managers
ecosystem.config.js

# Backup files
*.backup
*.bak

# Monorepo tools
.turbo/

# Local configuration
config/local.json
config/local.js

# Vercel
.vercel/

# Optional caches
.eslintcache
.node_repl_history
