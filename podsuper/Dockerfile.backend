# Backend Dockerfile
FROM node:18-alpine AS base

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY packages/types/package.json ./packages/types/
COPY packages/config/package.json ./packages/config/
COPY packages/ui/package.json ./packages/ui/
COPY apps/backend/package.json ./apps/backend/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY packages/ ./packages/
COPY apps/backend/ ./apps/backend/
COPY tsconfig.base.json ./

# Build shared packages
RUN pnpm --filter @repo/types build
RUN pnpm --filter @repo/config build
RUN pnpm --filter @repo/ui build

# Build backend
RUN pnpm --filter backend build

# Production stage
FROM node:18-alpine AS production

# Install pnpm
RUN npm install -g pnpm

WORKDIR /app

# Copy package files
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY packages/types/package.json ./packages/types/
COPY packages/config/package.json ./packages/config/
COPY packages/ui/package.json ./packages/ui/
COPY apps/backend/package.json ./apps/backend/

# Install production dependencies only
RUN pnpm install --frozen-lockfile --prod

# Copy built application
COPY --from=base /app/packages/types/dist ./packages/types/dist
COPY --from=base /app/packages/config/dist ./packages/config/dist
COPY --from=base /app/packages/ui/dist ./packages/ui/dist
COPY --from=base /app/apps/backend/dist ./apps/backend/dist
COPY --from=base /app/apps/backend/migrations ./apps/backend/migrations

# Copy package.json files for runtime
COPY --from=base /app/packages/types/package.json ./packages/types/
COPY --from=base /app/packages/config/package.json ./packages/config/
COPY --from=base /app/packages/ui/package.json ./packages/ui/

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/ || exit 1

# Start application
CMD ["node", "apps/backend/dist/index.js"]
