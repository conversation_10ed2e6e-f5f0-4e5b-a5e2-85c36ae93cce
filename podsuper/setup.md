# Setup Guide

## Prerequisites

Before running the application, you need to have the following installed:

1. **Node.js** (version 18 or higher)
2. **PostgreSQL** (version 13 or higher)
3. **Redis** (version 6 or higher)

## Database Setup

### Option 1: Using Docker (Recommended)

The easiest way to set up the required services is using Docker Compose:

```bash
# Start PostgreSQL and Redis
docker-compose up -d postgres redis

# Wait for services to be ready (about 10-15 seconds)
sleep 15

# Run migrations
npm run migrate
```

### Option 2: Local Installation

If you prefer to install PostgreSQL and Redis locally:

#### PostgreSQL Setup

1. Install PostgreSQL on your system
2. Create a database:
```sql
CREATE DATABASE design_fulfillment;
CREATE USER postgres WITH PASSWORD 'password';
GRANT ALL PRIVILEGES ON DATABASE design_fulfillment TO postgres;
```

#### Redis Setup

1. Install Redis on your system
2. Start Redis server:
```bash
redis-server
```

## Environment Configuration

1. Copy the environment file:
```bash
cp .env.example .env
```

2. Update the `.env` file with your actual API keys and database credentials:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=design_fulfillment
DB_USER=postgres
DB_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# API Keys (Replace with your actual keys)
KLING_AI_API_KEY=your-kling-ai-api-key
MOCKUP_AI_API_KEY=your-mockup-ai-api-key
PRINTIFY_API_KEY=your-printify-api-key
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
SHOPIFY_WEBHOOK_SECRET=your-shopify-webhook-secret
GEARMENT_API_KEY=your-gearment-api-key
GEARMENT_WEBHOOK_SECRET=your-gearment-webhook-secret

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your-r2-account-id
R2_ACCESS_KEY_ID=your-r2-access-key
R2_SECRET_ACCESS_KEY=your-r2-secret-key
R2_BUCKET_NAME=design-assets
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
```

## Installation and Setup

1. Install dependencies:
```bash
npm install
```

2. Run database migrations:
```bash
npm run migrate
```

3. Start the development server:
```bash
npm run dev
```

The API will be available at `http://localhost:3000`

## API Endpoints

Once the server is running, you can access:

- **Health Check**: `GET http://localhost:3000/`
- **API Documentation**: `GET http://localhost:3000/api`
- **Design Upload**: `POST http://localhost:3000/api/designs`
- **Orders**: `GET http://localhost:3000/api/orders`

## Testing the Setup

You can test if everything is working by making a request to the health endpoint:

```bash
curl http://localhost:3000/
```

You should get a response like:
```json
{
  "message": "Design to Fulfillment System API",
  "version": "1.0.0",
  "status": "running",
  "timestamp": "2025-07-31T05:00:00.000Z"
}
```

## Troubleshooting

### Database Connection Issues

If you get database connection errors:

1. Make sure PostgreSQL is running
2. Check your database credentials in `.env`
3. Ensure the database exists
4. Check if the user has proper permissions

### Redis Connection Issues

If you get Redis connection errors:

1. Make sure Redis is running
2. Check Redis host and port in `.env`
3. Test Redis connection: `redis-cli ping`

### Migration Issues

If migrations fail:

1. Check database connection
2. Ensure the user has CREATE TABLE permissions
3. Check if migrations table exists: `\dt` in psql

### API Key Issues

The system will start without API keys, but external integrations won't work:

1. Get API keys from respective services
2. Update `.env` file
3. Restart the server

## Next Steps

After setup is complete:

1. Configure webhook endpoints in Shopify and Gearment
2. Test file uploads and design management
3. Test the complete workflow with sample data
4. Set up monitoring and logging for production
