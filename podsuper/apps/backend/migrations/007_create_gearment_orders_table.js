/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('gearment_orders', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('order_id').notNullable();
    table.string('line_item_id').notNullable();
    table.string('gearment_order_id').notNullable();
    table.enum('status', [
      'pending', 'processing', 'printed', 'shipped', 'delivered', 'cancelled'
    ]).defaultTo('pending');
    table.string('tracking_number').nullable();
    table.string('tracking_url').nullable();
    table.string('tracking_company').nullable();
    table.timestamp('shipped_at').nullable();
    table.timestamp('delivered_at').nullable();
    table.timestamp('estimated_delivery').nullable();
    table.text('notes').nullable();
    table.json('gearment_data').nullable();
    table.decimal('production_cost', 10, 2).nullable();
    table.decimal('shipping_cost', 10, 2).nullable();
    table.timestamps(true, true);
    
    table.foreign('order_id').references('id').inTable('orders').onDelete('CASCADE');
    table.unique(['order_id', 'line_item_id']);
    table.index(['order_id']);
    table.index(['gearment_order_id']);
    table.index(['status']);
    table.index(['tracking_number']);
    table.index(['shipped_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('gearment_orders');
};
