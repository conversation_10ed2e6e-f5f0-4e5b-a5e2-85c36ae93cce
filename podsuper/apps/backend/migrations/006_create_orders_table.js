/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('orders', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('shopify_order_id').notNullable();
    table.uuid('shopify_store_id').notNullable();
    table.string('order_number').notNullable();
    table.string('customer_email').notNullable();
    table.string('customer_name').notNullable();
    table.json('shipping_address').notNullable();
    table.json('billing_address').notNullable();
    table.json('line_items').notNullable();
    table.decimal('total_price', 10, 2).notNullable();
    table.string('currency', 3).defaultTo('USD');
    table.enum('payment_status', [
      'pending', 'authorized', 'partially_paid', 'paid', 
      'partially_refunded', 'refunded', 'voided'
    ]).defaultTo('pending');
    table.enum('fulfillment_status', [
      'unfulfilled', 'partial', 'fulfilled'
    ]).defaultTo('unfulfilled');
    table.enum('order_status', [
      'pending', 'approved', 'processing', 'shipped', 
      'delivered', 'cancelled', 'refunded'
    ]).defaultTo('pending');
    table.text('notes').nullable();
    table.json('tags').defaultTo('[]');
    table.json('shopify_data').nullable();
    table.timestamp('shopify_created_at').nullable();
    table.timestamp('approved_at').nullable();
    table.timestamps(true, true);
    
    table.foreign('shopify_store_id').references('id').inTable('shopify_stores').onDelete('CASCADE');
    table.unique(['shopify_store_id', 'shopify_order_id']);
    table.index(['shopify_store_id']);
    table.index(['order_number']);
    table.index(['customer_email']);
    table.index(['payment_status']);
    table.index(['fulfillment_status']);
    table.index(['order_status']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('orders');
};
