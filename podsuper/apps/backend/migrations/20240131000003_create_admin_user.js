const bcrypt = require('bcryptjs');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Check if admin user already exists
  const existingAdmin = await knex('users').where('email', '<EMAIL>').first();
  
  if (!existingAdmin) {
    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    // Insert admin user
    await knex('users').insert({
      id: knex.raw('gen_random_uuid()'),
      email: '<EMAIL>',
      password_hash: hashedPassword,
      first_name: 'Admin',
      last_name: 'User',
      role: 'admin',
      is_active: true,
      email_verified_at: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    });
    
    console.log('✅ Admin user created successfully');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
  } else {
    console.log('ℹ️ Admin user already exists');
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Remove admin user
  await knex('users').where('email', '<EMAIL>').del();
  console.log('🗑️ Admin user removed');
};
