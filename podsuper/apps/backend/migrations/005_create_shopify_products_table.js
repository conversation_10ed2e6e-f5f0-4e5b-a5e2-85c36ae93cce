/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('shopify_products', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('design_id').notNullable();
    table.uuid('shopify_store_id').notNullable();
    table.string('shopify_product_id').notNullable();
    table.string('title').notNullable();
    table.text('description').nullable();
    table.decimal('price', 10, 2).notNullable();
    table.decimal('compare_at_price', 10, 2).nullable();
    table.json('tags').defaultTo('[]');
    table.enum('status', ['draft', 'active', 'archived']).defaultTo('draft');
    table.json('variants').defaultTo('[]');
    table.json('images').defaultTo('[]');
    table.json('shopify_data').nullable();
    table.timestamps(true, true);
    
    table.foreign('design_id').references('id').inTable('designs').onDelete('CASCADE');
    table.foreign('shopify_store_id').references('id').inTable('shopify_stores').onDelete('CASCADE');
    table.unique(['shopify_store_id', 'shopify_product_id']);
    table.index(['design_id']);
    table.index(['shopify_store_id']);
    table.index(['status']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('shopify_products');
};
