/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('designs', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name').notNullable();
    table.text('description').nullable();
    table.string('file_url').notNullable();
    table.string('file_type').notNullable();
    table.integer('file_size').notNullable();
    table.string('thumbnail_url').nullable();
    table.json('tags').defaultTo('[]');
    table.enum('status', ['draft', 'processing', 'ready', 'published', 'archived']).defaultTo('draft');
    table.uuid('user_id').notNullable();
    table.string('ai_video_url').nullable();
    table.json('ai_mockup_urls').defaultTo('[]');
    table.timestamps(true, true);
    
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.index(['user_id']);
    table.index(['status']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('designs');
};
