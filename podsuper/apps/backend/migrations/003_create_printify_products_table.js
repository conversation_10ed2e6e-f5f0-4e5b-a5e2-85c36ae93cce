/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('printify_products', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('design_id').notNullable();
    table.string('printify_product_id').notNullable();
    table.json('mockup_urls').defaultTo('[]');
    table.json('stored_mockup_urls').defaultTo('[]');
    table.enum('sync_status', ['pending', 'syncing', 'synced', 'failed']).defaultTo('pending');
    table.timestamp('last_sync_at').nullable();
    table.text('sync_error').nullable();
    table.json('printify_data').nullable();
    table.timestamps(true, true);
    
    table.foreign('design_id').references('id').inTable('designs').onDelete('CASCADE');
    table.unique(['design_id', 'printify_product_id']);
    table.index(['design_id']);
    table.index(['sync_status']);
    table.index(['printify_product_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('printify_products');
};
