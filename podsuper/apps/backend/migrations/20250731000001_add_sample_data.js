/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
  // Get test user ID
  const testUser = await knex('users').where('email', '<EMAIL>').first();
  if (!testUser) {
    console.log('Test user not found, skipping sample data creation');
    return;
  }

  // Check if sample store already exists
  const existingStore = await knex('shopify_stores').where('user_id', testUser.id).first();
  if (existingStore) {
    console.log('Sample data already exists');
    return;
  }

  // Create sample Shopify store
  const [storeId] = await knex('shopify_stores').insert({
    id: knex.raw('gen_random_uuid()'),
    name: 'Test Store',
    shop_domain: 'test-store.myshopify.com',
    access_token: 'test_access_token',
    webhook_verified: true,
    is_active: true,
    user_id: testUser.id,
    store_info: JSON.stringify({
      name: 'Test Store',
      email: '<EMAIL>',
      domain: 'test-store.myshopify.com'
    }),
    created_at: new Date(),
    updated_at: new Date()
  }).returning('id');

  // Create sample orders
  const orders = [
    {
      id: knex.raw('gen_random_uuid()'),
      shopify_order_id: 'order_001',
      shopify_store_id: storeId.id || storeId,
      order_number: '#1001',
      customer_email: '<EMAIL>',
      customer_name: 'John Doe',
      shipping_address: JSON.stringify({
        first_name: 'John',
        last_name: 'Doe',
        address1: '123 Main St',
        city: 'New York',
        province: 'NY',
        country: 'US',
        zip: '10001'
      }),
      billing_address: JSON.stringify({
        first_name: 'John',
        last_name: 'Doe',
        address1: '123 Main St',
        city: 'New York',
        province: 'NY',
        country: 'US',
        zip: '10001'
      }),
      line_items: JSON.stringify([
        {
          id: 'item_001',
          title: 'Custom T-Shirt',
          quantity: 2,
          price: '25.00'
        }
      ]),
      total_price: 50.00,
      currency: 'USD',
      payment_status: 'paid',
      fulfillment_status: 'unfulfilled',
      order_status: 'approved',
      shopify_created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      shopify_order_id: 'order_002',
      shopify_store_id: storeId.id || storeId,
      order_number: '#1002',
      customer_email: '<EMAIL>',
      customer_name: 'Jane Smith',
      shipping_address: JSON.stringify({
        first_name: 'Jane',
        last_name: 'Smith',
        address1: '456 Oak Ave',
        city: 'Los Angeles',
        province: 'CA',
        country: 'US',
        zip: '90210'
      }),
      billing_address: JSON.stringify({
        first_name: 'Jane',
        last_name: 'Smith',
        address1: '456 Oak Ave',
        city: 'Los Angeles',
        province: 'CA',
        country: 'US',
        zip: '90210'
      }),
      line_items: JSON.stringify([
        {
          id: 'item_002',
          title: 'Custom Hoodie',
          quantity: 1,
          price: '45.00'
        }
      ]),
      total_price: 45.00,
      currency: 'USD',
      payment_status: 'paid',
      fulfillment_status: 'fulfilled',
      order_status: 'delivered',
      shopify_created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      shopify_order_id: 'order_003',
      shopify_store_id: storeId.id || storeId,
      order_number: '#1003',
      customer_email: '<EMAIL>',
      customer_name: 'Bob Johnson',
      shipping_address: JSON.stringify({
        first_name: 'Bob',
        last_name: 'Johnson',
        address1: '789 Pine St',
        city: 'Chicago',
        province: 'IL',
        country: 'US',
        zip: '60601'
      }),
      billing_address: JSON.stringify({
        first_name: 'Bob',
        last_name: 'Johnson',
        address1: '789 Pine St',
        city: 'Chicago',
        province: 'IL',
        country: 'US',
        zip: '60601'
      }),
      line_items: JSON.stringify([
        {
          id: 'item_003',
          title: 'Custom Mug',
          quantity: 3,
          price: '15.00'
        }
      ]),
      total_price: 45.00,
      currency: 'USD',
      payment_status: 'pending',
      fulfillment_status: 'unfulfilled',
      order_status: 'pending',
      shopify_created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      updated_at: new Date()
    }
  ];

  await knex('orders').insert(orders);

  // Create sample designs
  const designs = [
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Cool T-Shirt Design',
      description: 'A cool design for t-shirts',
      file_url: '/uploads/design1.png',
      file_type: 'image/png',
      file_size: 1024000,
      thumbnail_url: '/uploads/design1_thumb.png',
      tags: JSON.stringify(['t-shirt', 'cool', 'graphic']),
      status: 'published',
      user_id: testUser.id,
      created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Hoodie Design',
      description: 'A design for hoodies',
      file_url: '/uploads/design2.png',
      file_type: 'image/png',
      file_size: 2048000,
      thumbnail_url: '/uploads/design2_thumb.png',
      tags: JSON.stringify(['hoodie', 'warm', 'winter']),
      status: 'ready',
      user_id: testUser.id,
      created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      updated_at: new Date()
    },
    {
      id: knex.raw('gen_random_uuid()'),
      name: 'Mug Design',
      description: 'A design for mugs',
      file_url: '/uploads/design3.png',
      file_type: 'image/png',
      file_size: 512000,
      thumbnail_url: '/uploads/design3_thumb.png',
      tags: JSON.stringify(['mug', 'coffee', 'morning']),
      status: 'draft',
      user_id: testUser.id,
      created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      updated_at: new Date()
    }
  ];

  await knex('designs').insert(designs);

  console.log('✅ Sample data created successfully');
  console.log('📊 Created 1 store, 3 orders, and 3 designs for test user');
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
  // Get test user ID
  const testUser = await knex('users').where('email', '<EMAIL>').first();
  if (!testUser) {
    return;
  }

  // Delete sample data
  await knex('orders').whereIn('shopify_store_id', 
    knex('shopify_stores').select('id').where('user_id', testUser.id)
  ).del();
  
  await knex('shopify_stores').where('user_id', testUser.id).del();
  await knex('designs').where('user_id', testUser.id).del();

  console.log('🗑️ Sample data removed');
};
