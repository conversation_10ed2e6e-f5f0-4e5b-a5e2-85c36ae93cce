/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('shopify_stores', function(table) {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name').notNullable();
    table.string('shop_domain').unique().notNullable();
    table.text('access_token').notNullable();
    table.boolean('webhook_verified').defaultTo(false);
    table.boolean('is_active').defaultTo(true);
    table.uuid('user_id').notNullable();
    table.json('store_info').nullable();
    table.timestamp('last_sync_at').nullable();
    table.timestamps(true, true);
    
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.index(['user_id']);
    table.index(['shop_domain']);
    table.index(['is_active']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('shopify_stores');
};
