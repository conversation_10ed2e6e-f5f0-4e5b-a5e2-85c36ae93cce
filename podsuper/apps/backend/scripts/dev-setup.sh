#!/bin/bash

# Development Setup Script for Design to Fulfillment System

set -e

echo "🚀 Setting up Design to Fulfillment System for development..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update .env file with your actual API keys before running the application"
else
    echo "✅ .env file already exists"
fi

# Install dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Start database services
echo "🐘 Starting PostgreSQL and Redis with Docker..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for database services to be ready..."
sleep 15

# Check if PostgreSQL is ready
echo "🔍 Checking PostgreSQL connection..."
until docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U postgres; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

# Check if Redis is ready
echo "🔍 Checking Redis connection..."
until docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping; do
    echo "Waiting for Redis..."
    sleep 2
done

echo "✅ Database services are ready"

# Run migrations
echo "🗄️  Running database migrations..."
npm run migrate

echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update .env file with your actual API keys"
echo "2. Start the development server: npm run dev"
echo "3. Visit http://localhost:3000 to test the API"
echo ""
echo "🔧 Useful commands:"
echo "- Start dev server: npm run dev"
echo "- View logs: docker-compose -f docker-compose.dev.yml logs -f"
echo "- Stop services: docker-compose -f docker-compose.dev.yml down"
echo "- Reset database: docker-compose -f docker-compose.dev.yml down -v && ./scripts/dev-setup.sh"
