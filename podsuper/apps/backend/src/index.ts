import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import { config } from '@repo/config';
import routes from '@/routes';
import { handleUploadError } from '@/middleware/upload';
import { errorHandler } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/logger';
import { WebSocketService } from '@/services/websocketService';
import knex from 'knex';
import databaseConfig from '@/config/database';

// Create Express app and HTTP server
const app = express();
const server = createServer(app);
const PORT = config.port;

// Database connection
const db = knex(databaseConfig.development);

// Test database connection
db.raw('SELECT 1')
  .then(() => {
    console.log('✅ Database connected successfully');
  })
  .catch((error) => {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  });

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] // Replace with your actual domain
    : ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Request logging
app.use(requestLogger);

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Raw body parser for webhooks
app.use('/api/webhooks', express.raw({ type: 'application/json' }));

// Health check endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Design to Fulfillment System API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
  });
});

// API routes
app.use('/api', routes);

// Upload error handling
app.use(handleUploadError);

// Global error handler
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.originalUrl,
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  
  try {
    await db.destroy();
    console.log('Database connections closed');
  } catch (error) {
    console.error('Error closing database connections:', error);
  }
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  
  try {
    await db.destroy();
    console.log('Database connections closed');
  } catch (error) {
    console.error('Error closing database connections:', error);
  }
  
  process.exit(0);
});

// Initialize WebSocket service
const wsService = new WebSocketService(server);

// Make WebSocket service available globally
declare global {
  var wsService: WebSocketService;
}
global.wsService = wsService;

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📝 Environment: ${config.nodeEnv}`);
  console.log(`🔗 API URL: http://localhost:${PORT}/api`);
  console.log(`🔌 WebSocket URL: ws://localhost:${PORT}`);
  console.log(`❤️  Health check: http://localhost:${PORT}/`);
});

export default app;
