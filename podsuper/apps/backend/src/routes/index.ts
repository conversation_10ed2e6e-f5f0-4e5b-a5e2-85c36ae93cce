import { Router } from 'express';
import { DesignController } from '@/controllers/DesignController';
import { OrderController } from '@/controllers/OrderController';
import { WebhookController } from '@/controllers/WebhookController';
import { DashboardController } from '@/controllers/dashboardController';
import { AuthController } from '@/controllers/AuthController';
import { AdminController } from '@/controllers/AdminController';
import { NotificationController } from '@/controllers/NotificationController';
import { DesignUploadController, uploadMiddleware as designUploadMiddleware } from '@/controllers/DesignUploadController';
import { authMiddleware } from '@/middleware/auth';
import { uploadMiddleware } from '@/middleware/upload';
import { validateRequest } from '@/middleware/validation';
import { designValidation, orderValidation } from '@/validation/schemas';

const router = Router();

// Initialize controllers
const designController = new DesignController();
const orderController = new OrderController();
const webhookController = new WebhookController();
const dashboardController = new DashboardController();
const authController = new AuthController();
const adminController = new AdminController();
const notificationController = new NotificationController();
const designUploadController = new DesignUploadController();

// Health check
router.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Root endpoint
router.get('/', (req, res) => {
  res.json({
    message: 'Design to Fulfillment System API',
    version: '1.0.0',
    status: 'running'
  });
});

// Auth routes (public)
router.post('/auth/login', authController.login.bind(authController));
router.post('/auth/register', authController.register.bind(authController));
router.post('/auth/logout', authController.logout.bind(authController));

// Protected auth routes
router.get('/auth/me', authMiddleware, authController.me.bind(authController));
router.post('/auth/refresh', authMiddleware, authController.refreshToken.bind(authController));

// Design routes
router.post('/designs',
  authMiddleware,
  uploadMiddleware.single('file'),
  validateRequest(designValidation.create),
  designController.createDesign.bind(designController)
);

router.get('/designs',
  authMiddleware,
  designController.getDesigns.bind(designController)
);

router.get('/designs/:id',
  authMiddleware,
  designController.getDesign.bind(designController)
);

router.put('/designs/:id',
  authMiddleware,
  validateRequest(designValidation.update),
  designController.updateDesign.bind(designController)
);

router.delete('/designs/:id',
  authMiddleware,
  designController.deleteDesign.bind(designController)
);

router.post('/designs/:id/ai-video',
  authMiddleware,
  validateRequest(designValidation.aiVideo),
  designController.generateAIVideo.bind(designController)
);

router.post('/designs/:id/ai-mockups',
  authMiddleware,
  validateRequest(designValidation.aiMockup),
  designController.generateAIMockups.bind(designController)
);

// Order routes
router.get('/orders',
  authMiddleware,
  orderController.getOrders.bind(orderController)
);

router.get('/orders/:id',
  authMiddleware,
  orderController.getOrder.bind(orderController)
);

router.post('/orders/:id/approve',
  authMiddleware,
  validateRequest(orderValidation.approve),
  orderController.approveOrder.bind(orderController)
);

router.post('/orders/:id/cancel',
  authMiddleware,
  validateRequest(orderValidation.cancel),
  orderController.cancelOrder.bind(orderController)
);

router.get('/orders/stats',
  authMiddleware,
  orderController.getOrderStats.bind(orderController)
);

// Dashboard routes
router.get('/dashboard/stats',
  authMiddleware,
  dashboardController.getStats.bind(dashboardController)
);

router.get('/dashboard/activity',
  authMiddleware,
  dashboardController.getRecentActivity.bind(dashboardController)
);

router.get('/dashboard/revenue-chart',
  authMiddleware,
  dashboardController.getRevenueChart.bind(dashboardController)
);

router.get('/dashboard/orders-timeline',
  authMiddleware,
  dashboardController.getOrdersTimeline.bind(dashboardController)
);

router.get('/dashboard/orders-status',
  authMiddleware,
  dashboardController.getOrdersStatus.bind(dashboardController)
);

router.get('/dashboard/export',
  authMiddleware,
  dashboardController.exportData.bind(dashboardController)
);

// Webhook routes (no auth middleware for webhooks)
router.post('/webhooks/shopify/orders/create',
  webhookController.handleShopifyOrderCreated.bind(webhookController)
);

router.post('/webhooks/shopify/orders/updated',
  webhookController.handleShopifyOrderUpdated.bind(webhookController)
);

router.post('/webhooks/shopify/orders/paid',
  webhookController.handleShopifyOrderPaid.bind(webhookController)
);

router.post('/webhooks/shopify/orders/cancelled',
  webhookController.handleShopifyOrderCancelled.bind(webhookController)
);

router.post('/webhooks/gearment/orders/update',
  webhookController.handleGearmentOrderUpdate.bind(webhookController)
);

// Admin routes (admin only)
router.get('/admin/users', authMiddleware, adminController.getUsers.bind(adminController));
router.post('/admin/users', authMiddleware, adminController.createUser.bind(adminController));
router.put('/admin/users/:id', authMiddleware, adminController.updateUser.bind(adminController));
router.delete('/admin/users/:id', authMiddleware, adminController.deleteUser.bind(adminController));
router.patch('/admin/users/:id/toggle-status', authMiddleware, adminController.toggleUserStatus.bind(adminController));

// Notification routes
router.get('/notifications', authMiddleware, notificationController.getNotifications.bind(notificationController));
router.patch('/notifications/:id/read', authMiddleware, notificationController.markAsRead.bind(notificationController));
router.patch('/notifications/:id/unread', authMiddleware, notificationController.markAsUnread.bind(notificationController));
router.patch('/notifications/mark-all-read', authMiddleware, notificationController.markAllAsRead.bind(notificationController));
router.delete('/notifications/:id', authMiddleware, notificationController.deleteNotification.bind(notificationController));
router.delete('/notifications/clear-all', authMiddleware, notificationController.clearAll.bind(notificationController));
router.post('/notifications', authMiddleware, notificationController.createNotification.bind(notificationController));

// Design upload routes
router.post('/designs/upload', authMiddleware, designUploadMiddleware.single('file'), designUploadController.uploadDesign.bind(designUploadController));
router.get('/designs/user', authMiddleware, designUploadController.getUserDesigns.bind(designUploadController));
router.get('/designs/all', authMiddleware, designUploadController.getAllDesigns.bind(designUploadController));
router.patch('/designs/:id/status', authMiddleware, designUploadController.updateDesignStatus.bind(designUploadController));
router.delete('/designs/:id', authMiddleware, designUploadController.deleteDesign.bind(designUploadController));
router.get('/designs/:id/file', designUploadController.getDesignFile.bind(designUploadController));
router.get('/designs/categories', designUploadController.getCategories.bind(designUploadController));

// Advanced analytics (admin only)
router.get('/dashboard/advanced-analytics', authMiddleware, adminController.getAdvancedAnalytics.bind(adminController));

export default router;
