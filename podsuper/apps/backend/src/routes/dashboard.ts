import { Router } from 'express';
import { authMiddleware } from '@/middleware/auth';
import { DashboardController } from '@/controllers/dashboardController';

const router = Router();
const dashboardController = new DashboardController();

// Apply authentication middleware to all dashboard routes
router.use(authMiddleware);

// Dashboard stats endpoint
router.get('/stats', dashboardController.getStats);

// Dashboard activity endpoint
router.get('/activity', dashboardController.getRecentActivity);

// Revenue chart data endpoint
router.get('/revenue-chart', dashboardController.getRevenueChart);

// Orders timeline data endpoint
router.get('/orders-timeline', dashboardController.getOrdersTimeline);

// Orders status data endpoint
router.get('/orders-status', dashboardController.getOrdersStatus);

// Export dashboard data endpoint
router.get('/export', dashboardController.exportData);

export default router;
