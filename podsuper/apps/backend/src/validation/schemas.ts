import Joi from 'joi';

// Design validation schemas
export const designValidation = {
  create: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    description: Joi.string().max(1000).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(20).optional(),
  }),

  update: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    description: Joi.string().max(1000).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(20).optional(),
    status: Joi.string().valid('draft', 'processing', 'ready', 'published', 'archived').optional(),
  }),

  aiVideo: Joi.object({
    prompt: Joi.string().max(500).optional(),
    duration: Joi.number().min(5).max(60).optional(),
    style: Joi.string().valid('modern', 'classic', 'minimalist', 'vibrant').optional(),
  }),

  aiMockup: Joi.object({
    product_type: Joi.string().valid('t-shirt', 'hoodie', 'mug', 'poster', 'phone-case').required(),
    style: Joi.string().valid('realistic', 'lifestyle', 'studio', 'flat-lay').optional(),
    background: Joi.string().valid('white', 'transparent', 'lifestyle', 'custom').optional(),
  }),
};

// Order validation schemas
export const orderValidation = {
  approve: Joi.object({
    notes: Joi.string().max(1000).optional(),
  }),

  cancel: Joi.object({
    reason: Joi.string().max(500).required(),
  }),

  updateStatus: Joi.object({
    status: Joi.string().valid(
      'pending', 'approved', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'
    ).required(),
    notes: Joi.string().max(1000).optional(),
  }),
};

// Shopify store validation schemas
export const shopifyStoreValidation = {
  create: Joi.object({
    name: Joi.string().min(1).max(255).required(),
    shop_domain: Joi.string().hostname().required(),
    access_token: Joi.string().required(),
  }),

  update: Joi.object({
    name: Joi.string().min(1).max(255).optional(),
    is_active: Joi.boolean().optional(),
  }),
};

// Product validation schemas
export const productValidation = {
  create: Joi.object({
    design_id: Joi.string().uuid().required(),
    shopify_store_id: Joi.string().uuid().required(),
    title: Joi.string().min(1).max(255).required(),
    description: Joi.string().max(5000).optional(),
    price: Joi.number().min(0).max(10000).required(),
    compare_at_price: Joi.number().min(0).max(10000).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(20).optional(),
    variants: Joi.array().items(
      Joi.object({
        title: Joi.string().required(),
        price: Joi.number().min(0).required(),
        sku: Joi.string().max(100).optional(),
        inventory_quantity: Joi.number().min(0).default(0),
        size: Joi.string().max(50).optional(),
        color: Joi.string().max(50).optional(),
      })
    ).min(1).required(),
  }),

  update: Joi.object({
    title: Joi.string().min(1).max(255).optional(),
    description: Joi.string().max(5000).optional(),
    price: Joi.number().min(0).max(10000).optional(),
    compare_at_price: Joi.number().min(0).max(10000).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(20).optional(),
    status: Joi.string().valid('draft', 'active', 'archived').optional(),
  }),
};

// User validation schemas
export const userValidation = {
  register: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).max(128).required(),
    first_name: Joi.string().min(1).max(100).required(),
    last_name: Joi.string().min(1).max(100).required(),
  }),

  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required(),
  }),

  updateProfile: Joi.object({
    first_name: Joi.string().min(1).max(100).optional(),
    last_name: Joi.string().min(1).max(100).optional(),
  }),

  changePassword: Joi.object({
    current_password: Joi.string().required(),
    new_password: Joi.string().min(8).max(128).required(),
  }),
};

// Query validation schemas
export const queryValidation = {
  pagination: Joi.object({
    page: Joi.number().min(1).default(1),
    limit: Joi.number().min(1).max(100).default(20),
  }),

  dateRange: Joi.object({
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().min(Joi.ref('start_date')).optional(),
  }),

  orderFilters: Joi.object({
    status: Joi.string().valid(
      'pending', 'approved', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'
    ).optional(),
    payment_status: Joi.string().valid(
      'pending', 'authorized', 'partially_paid', 'paid', 'partially_refunded', 'refunded', 'voided'
    ).optional(),
    fulfillment_status: Joi.string().valid('unfulfilled', 'partial', 'fulfilled').optional(),
    shop_id: Joi.string().uuid().optional(),
  }),

  designFilters: Joi.object({
    status: Joi.string().valid('draft', 'processing', 'ready', 'published', 'archived').optional(),
    search: Joi.string().max(255).optional(),
  }),
};

// Webhook validation schemas
export const webhookValidation = {
  shopifyOrder: Joi.object({
    id: Joi.alternatives().try(Joi.string(), Joi.number()).required(),
    order_number: Joi.alternatives().try(Joi.string(), Joi.number()).optional(),
    name: Joi.string().optional(),
    email: Joi.string().email().optional(),
    created_at: Joi.string().isoDate().required(),
    updated_at: Joi.string().isoDate().optional(),
    total_price: Joi.alternatives().try(Joi.string(), Joi.number()).required(),
    currency: Joi.string().length(3).optional(),
    financial_status: Joi.string().optional(),
    fulfillment_status: Joi.string().optional(),
    customer: Joi.object().optional(),
    shipping_address: Joi.object().optional(),
    billing_address: Joi.object().optional(),
    line_items: Joi.array().optional(),
    note: Joi.string().optional(),
    tags: Joi.string().optional(),
    cancel_reason: Joi.string().optional(),
  }),

  gearmentOrder: Joi.object({
    order_id: Joi.string().required(),
    status: Joi.string().valid(
      'pending', 'processing', 'printed', 'shipped', 'delivered', 'cancelled'
    ).required(),
    tracking_number: Joi.string().optional(),
    tracking_url: Joi.string().uri().optional(),
    tracking_company: Joi.string().optional(),
    shipped_at: Joi.string().isoDate().optional(),
    delivered_at: Joi.string().isoDate().optional(),
    notes: Joi.string().optional(),
  }),
};
