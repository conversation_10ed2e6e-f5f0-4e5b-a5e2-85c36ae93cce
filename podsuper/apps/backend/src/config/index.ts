import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // Server Configuration
  port: parseInt(process.env.PORT || '3000'),
  nodeEnv: process.env.NODE_ENV || 'development',

  // Database Configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    name: process.env.DB_NAME || 'design_fulfillment',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  },

  // Redis Configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || '',
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  },

  // Cloudflare R2 Configuration
  r2: {
    accountId: process.env.R2_ACCOUNT_ID!,
    accessKeyId: process.env.R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
    bucketName: process.env.R2_BUCKET_NAME || 'design-assets',
    endpoint: process.env.R2_ENDPOINT!,
  },

  // Kling AI Configuration
  klingAI: {
    apiKey: process.env.KLING_AI_API_KEY!,
    baseUrl: process.env.KLING_AI_BASE_URL || 'https://api.kling.ai',
  },

  // AI Mockup Configuration
  mockupAI: {
    apiKey: process.env.MOCKUP_AI_API_KEY!,
    baseUrl: process.env.MOCKUP_AI_BASE_URL || 'https://api.mockup-ai.com',
  },

  // Printify Configuration
  printify: {
    apiKey: process.env.PRINTIFY_API_KEY!,
    baseUrl: process.env.PRINTIFY_BASE_URL || 'https://api.printify.com/v1',
  },

  // Shopify Configuration
  shopify: {
    apiKey: process.env.SHOPIFY_API_KEY!,
    apiSecret: process.env.SHOPIFY_API_SECRET!,
    webhookSecret: process.env.SHOPIFY_WEBHOOK_SECRET!,
  },

  // Gearment Configuration
  gearment: {
    apiKey: process.env.GEARMENT_API_KEY!,
    baseUrl: process.env.GEARMENT_BASE_URL || 'https://api.gearment.com/v1',
    webhookSecret: process.env.GEARMENT_WEBHOOK_SECRET!,
  },

  // File Upload Configuration
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '********'), // 10MB
    allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/svg+xml,application/pdf').split(','),
  },
};

// Validate required environment variables
const requiredEnvVars = [
  'R2_ACCOUNT_ID',
  'R2_ACCESS_KEY_ID',
  'R2_SECRET_ACCESS_KEY',
  'R2_ENDPOINT',
  'KLING_AI_API_KEY',
  'MOCKUP_AI_API_KEY',
  'PRINTIFY_API_KEY',
  'SHOPIFY_API_KEY',
  'SHOPIFY_API_SECRET',
  'SHOPIFY_WEBHOOK_SECRET',
  'GEARMENT_API_KEY',
  'GEARMENT_WEBHOOK_SECRET',
];

if (config.nodeEnv === 'production') {
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }
}
