import { Knex } from 'knex';
import knex from 'knex';
import { database, nodeEnv } from '@repo/config';

const config: { [key: string]: Knex.Config } = {
  development: {
    client: 'postgresql',
    connection: {
      host: database.host,
      port: database.port,
      database: database.name,
      user: database.user,
      password: database.password,
    },
    pool: {
      min: 2,
      max: 10,
    },
    migrations: {
      tableName: 'knex_migrations',
      directory: './migrations',
    },
    seeds: {
      directory: './seeds',
    },
  },
  production: {
    client: 'postgresql',
    connection: {
      host: database.host,
      port: database.port,
      database: database.name,
      user: database.user,
      password: database.password,
      ssl: database.ssl ? { rejectUnauthorized: false } : false,
    },
    pool: {
      min: 2,
      max: 20,
    },
    migrations: {
      tableName: 'knex_migrations',
      directory: './migrations',
    },
  },
};

export default config;

// Create database connection instance
const environment = nodeEnv || 'development';
export const db = knex(config[environment]);
