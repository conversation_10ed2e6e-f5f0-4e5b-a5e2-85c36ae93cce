// Base Types
export interface BaseEntity {
  id: string;
  created_at: Date;
  updated_at: Date;
}

// Design Types
export interface Design extends BaseEntity {
  name: string;
  description?: string;
  file_url: string;
  file_type: string;
  file_size: number;
  thumbnail_url?: string;
  tags: string[];
  status: DesignStatus;
  user_id: string;
  ai_video_url?: string;
  ai_mockup_urls: string[];
}

export enum DesignStatus {
  DRAFT = 'draft',
  PROCESSING = 'processing',
  READY = 'ready',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

// Printify Types
export interface PrintifyProduct extends BaseEntity {
  design_id: string;
  printify_product_id: string;
  mockup_urls: string[];
  stored_mockup_urls: string[];
  sync_status: SyncStatus;
  last_sync_at?: Date;
}

export enum SyncStatus {
  PENDING = 'pending',
  SYNCING = 'syncing',
  SYNCED = 'synced',
  FAILED = 'failed'
}

// Shopify Types
export interface ShopifyStore extends BaseEntity {
  name: string;
  shop_domain: string;
  access_token: string;
  webhook_verified: boolean;
  is_active: boolean;
  user_id: string;
}

export interface ShopifyProduct extends BaseEntity {
  design_id: string;
  shopify_store_id: string;
  shopify_product_id: string;
  title: string;
  description: string;
  price: number;
  compare_at_price?: number;
  tags: string[];
  status: ProductStatus;
  variants: ShopifyVariant[];
}

export interface ShopifyVariant {
  id: string;
  shopify_variant_id: string;
  title: string;
  price: number;
  sku: string;
  inventory_quantity: number;
  size?: string;
  color?: string;
}

export enum ProductStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  ARCHIVED = 'archived'
}

// Order Types
export interface Order extends BaseEntity {
  shopify_order_id: string;
  shopify_store_id: string;
  order_number: string;
  customer_email: string;
  customer_name: string;
  shipping_address: ShippingAddress;
  billing_address: BillingAddress;
  line_items: OrderLineItem[];
  total_price: number;
  currency: string;
  payment_status: PaymentStatus;
  fulfillment_status: FulfillmentStatus;
  order_status: OrderStatus;
  notes?: string;
  tags: string[];
}

export interface OrderLineItem {
  id: string;
  shopify_line_item_id: string;
  product_id: string;
  variant_id: string;
  design_id: string;
  title: string;
  quantity: number;
  price: number;
  sku: string;
  gearment_order_id?: string;
  tracking_number?: string;
  tracking_url?: string;
}

export interface ShippingAddress {
  first_name: string;
  last_name: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  province: string;
  country: string;
  zip: string;
  phone?: string;
}

export interface BillingAddress extends ShippingAddress {}

export enum PaymentStatus {
  PENDING = 'pending',
  AUTHORIZED = 'authorized',
  PARTIALLY_PAID = 'partially_paid',
  PAID = 'paid',
  PARTIALLY_REFUNDED = 'partially_refunded',
  REFUNDED = 'refunded',
  VOIDED = 'voided'
}

export enum FulfillmentStatus {
  UNFULFILLED = 'unfulfilled',
  PARTIAL = 'partial',
  FULFILLED = 'fulfilled'
}

export enum OrderStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

// Gearment Types
export interface GearmentOrder extends BaseEntity {
  order_id: string;
  line_item_id: string;
  gearment_order_id: string;
  status: GearmentOrderStatus;
  tracking_number?: string;
  tracking_url?: string;
  shipped_at?: Date;
  delivered_at?: Date;
  notes?: string;
}

export enum GearmentOrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  PRINTED = 'printed',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

// AI Integration Types
export interface AIVideoRequest {
  design_id: string;
  prompt?: string;
  duration?: number;
  style?: string;
}

export interface AIVideoResponse {
  video_url: string;
  thumbnail_url: string;
  duration: number;
  status: string;
}

export interface AIMockupRequest {
  design_id: string;
  product_type: string;
  style?: string;
  background?: string;
}

export interface AIMockupResponse {
  mockup_urls: string[];
  status: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Job Types
export interface JobData {
  type: string;
  payload: any;
  retries?: number;
  delay?: number;
}

export enum JobType {
  SYNC_PRINTIFY = 'sync_printify',
  GENERATE_AI_VIDEO = 'generate_ai_video',
  GENERATE_AI_MOCKUP = 'generate_ai_mockup',
  PROCESS_ORDER = 'process_order',
  CREATE_GEARMENT_ORDER = 'create_gearment_order',
  UPDATE_TRACKING = 'update_tracking',
  STORE_ASSETS = 'store_assets'
}
