import axios, { AxiosInstance } from 'axios';
import { config } from '@/config';
import { PrintifyProduct, SyncStatus } from '@/types';

export class PrintifyService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.printify.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.printify.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });
  }

  /**
   * Get available print providers
   */
  async getPrintProviders(): Promise<any[]> {
    try {
      const response = await this.client.get('/print_providers.json');
      return response.data;
    } catch (error) {
      console.error('Error fetching print providers:', error);
      throw new Error('Failed to fetch print providers');
    }
  }

  /**
   * Get available products from a print provider
   */
  async getProducts(printProviderId: number): Promise<any[]> {
    try {
      const response = await this.client.get(`/catalog/print_providers/${printProviderId}/products.json`);
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  /**
   * Get product details including variants
   */
  async getProductDetails(printProviderId: number, productId: number): Promise<any> {
    try {
      const response = await this.client.get(
        `/catalog/print_providers/${printProviderId}/products/${productId}.json`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching product details:', error);
      throw new Error('Failed to fetch product details');
    }
  }

  /**
   * Upload image to Printify
   */
  async uploadImage(imageUrl: string, fileName: string): Promise<string> {
    try {
      const response = await this.client.post('/uploads/images.json', {
        file_name: fileName,
        url: imageUrl,
      });
      return response.data.id;
    } catch (error) {
      console.error('Error uploading image to Printify:', error);
      throw new Error('Failed to upload image to Printify');
    }
  }

  /**
   * Create a product on Printify
   */
  async createProduct(productData: {
    title: string;
    description: string;
    tags: string[];
    images: Array<{
      src: string;
      position: string;
      x: number;
      y: number;
      scale: number;
      angle: number;
    }>;
    variants: Array<{
      id: number;
      price: number;
      is_enabled: boolean;
    }>;
    print_areas: Array<{
      variant_ids: number[];
      placeholders: Array<{
        position: string;
        images: Array<{
          id: string;
          x: number;
          y: number;
          scale: number;
          angle: number;
        }>;
      }>;
    }>;
    print_provider_id: number;
    blueprint_id: number;
  }): Promise<any> {
    try {
      const response = await this.client.post('/shops/{shop_id}/products.json', productData);
      return response.data;
    } catch (error) {
      console.error('Error creating product on Printify:', error);
      throw new Error('Failed to create product on Printify');
    }
  }

  /**
   * Get product mockups
   */
  async getProductMockups(productId: string): Promise<string[]> {
    try {
      const response = await this.client.get(`/shops/{shop_id}/products/${productId}/mockups.json`);
      return response.data.map((mockup: any) => mockup.src);
    } catch (error) {
      console.error('Error fetching product mockups:', error);
      throw new Error('Failed to fetch product mockups');
    }
  }

  /**
   * Sync design with Printify and get mockups
   */
  async syncDesign(designData: {
    designId: string;
    imageUrl: string;
    fileName: string;
    productType: string;
    title: string;
    description: string;
    tags: string[];
  }): Promise<{
    printifyProductId: string;
    mockupUrls: string[];
  }> {
    try {
      // Step 1: Upload image to Printify
      const imageId = await this.uploadImage(designData.imageUrl, designData.fileName);

      // Step 2: Find suitable print provider and product
      const printProviders = await this.getPrintProviders();
      const printProvider = printProviders.find(p => p.title.toLowerCase().includes('printful')) || printProviders[0];
      
      const products = await this.getProducts(printProvider.id);
      const product = products.find(p => 
        p.title.toLowerCase().includes(designData.productType.toLowerCase())
      ) || products[0];

      const productDetails = await this.getProductDetails(printProvider.id, product.id);

      // Step 3: Create product with design
      const productData = {
        title: designData.title,
        description: designData.description,
        tags: designData.tags,
        images: [{
          src: designData.imageUrl,
          position: 'front',
          x: 0.5,
          y: 0.5,
          scale: 1,
          angle: 0,
        }],
        variants: productDetails.variants.map((variant: any) => ({
          id: variant.id,
          price: Math.round(variant.price * 2.5), // 2.5x markup
          is_enabled: true,
        })),
        print_areas: [{
          variant_ids: productDetails.variants.map((v: any) => v.id),
          placeholders: [{
            position: 'front',
            images: [{
              id: imageId,
              x: 0.5,
              y: 0.5,
              scale: 1,
              angle: 0,
            }],
          }],
        }],
        print_provider_id: printProvider.id,
        blueprint_id: product.blueprint_id,
      };

      const createdProduct = await this.createProduct(productData);

      // Step 4: Get mockups
      const mockupUrls = await this.getProductMockups(createdProduct.id);

      return {
        printifyProductId: createdProduct.id,
        mockupUrls,
      };
    } catch (error) {
      console.error('Error syncing design with Printify:', error);
      throw new Error('Failed to sync design with Printify');
    }
  }

  /**
   * Update product on Printify
   */
  async updateProduct(productId: string, updateData: any): Promise<any> {
    try {
      const response = await this.client.put(`/shops/{shop_id}/products/${productId}.json`, updateData);
      return response.data;
    } catch (error) {
      console.error('Error updating product on Printify:', error);
      throw new Error('Failed to update product on Printify');
    }
  }

  /**
   * Delete product from Printify
   */
  async deleteProduct(productId: string): Promise<void> {
    try {
      await this.client.delete(`/shops/{shop_id}/products/${productId}.json`);
    } catch (error) {
      console.error('Error deleting product from Printify:', error);
      throw new Error('Failed to delete product from Printify');
    }
  }

  /**
   * Get product orders
   */
  async getProductOrders(productId: string): Promise<any[]> {
    try {
      const response = await this.client.get(`/shops/{shop_id}/orders.json?product_id=${productId}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching product orders:', error);
      throw new Error('Failed to fetch product orders');
    }
  }

  /**
   * Calculate shipping costs
   */
  async calculateShipping(productId: string, address: any): Promise<any> {
    try {
      const response = await this.client.post('/shops/{shop_id}/orders/shipping.json', {
        line_items: [{
          product_id: productId,
          variant_id: 1,
          quantity: 1,
        }],
        address_to: address,
      });
      return response.data;
    } catch (error) {
      console.error('Error calculating shipping:', error);
      throw new Error('Failed to calculate shipping');
    }
  }

  /**
   * Get product pricing
   */
  async getProductPricing(printProviderId: number, blueprintId: number, variants: number[]): Promise<any> {
    try {
      const response = await this.client.post('/catalog/pricing.json', {
        print_provider_id: printProviderId,
        blueprint_id: blueprintId,
        variant_ids: variants,
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching product pricing:', error);
      throw new Error('Failed to fetch product pricing');
    }
  }

  /**
   * Batch sync multiple designs
   */
  async batchSyncDesigns(designs: Array<{
    designId: string;
    imageUrl: string;
    fileName: string;
    productType: string;
    title: string;
    description: string;
    tags: string[];
  }>): Promise<Array<{
    designId: string;
    success: boolean;
    printifyProductId?: string;
    mockupUrls?: string[];
    error?: string;
  }>> {
    const results = await Promise.allSettled(
      designs.map(async (design) => {
        try {
          const result = await this.syncDesign(design);
          return {
            designId: design.designId,
            success: true,
            printifyProductId: result.printifyProductId,
            mockupUrls: result.mockupUrls,
          };
        } catch (error) {
          return {
            designId: design.designId,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      })
    );

    return results.map((result) => 
      result.status === 'fulfilled' ? result.value : {
        designId: 'unknown',
        success: false,
        error: 'Promise rejected',
      }
    );
  }
}
