import { db } from '@/config/database';
import { DashboardStats, RecentActivity, OrderStatus } from '@repo/types';
import { logger } from '@/middleware/logger';

export class DashboardService {
  async getDashboardStats(userId: string, from?: string, to?: string): Promise<DashboardStats> {
    try {
      const fromDate = from ? new Date(from) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const toDate = to ? new Date(to) : new Date();

      // Get total revenue (join with shopify_stores to filter by user)
      const revenueResult = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .whereBetween('orders.created_at', [fromDate, toDate])
        .whereIn('orders.payment_status', ['paid', 'partially_paid'])
        .sum('orders.total_price as total_revenue')
        .first();

      // Get total orders (join with shopify_stores to filter by user)
      const ordersResult = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .whereBetween('orders.created_at', [fromDate, toDate])
        .count('* as total_orders')
        .first();

      // Get total designs
      const designsResult = await db('designs')
        .where('user_id', userId)
        .count('* as total_designs')
        .first();

      // Get active designs
      const activeDesignsResult = await db('designs')
        .where('user_id', userId)
        .whereIn('status', ['ready', 'published'])
        .count('* as active_designs')
        .first();

      // Get pending orders (join with shopify_stores to filter by user)
      const pendingOrdersResult = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .where('orders.order_status', 'pending')
        .count('* as pending_orders')
        .first();

      // Get connected stores
      const storesResult = await db('shopify_stores')
        .where('user_id', userId)
        .where('is_active', true)
        .count('* as connected_stores')
        .first();

      return {
        total_designs: parseInt(String(designsResult?.total_designs || '0')),
        total_orders: parseInt(String(ordersResult?.total_orders || '0')),
        total_revenue: parseFloat(String(revenueResult?.total_revenue || '0')),
        pending_orders: parseInt(String(pendingOrdersResult?.pending_orders || '0')),
        active_designs: parseInt(String(activeDesignsResult?.active_designs || '0')),
        connected_stores: parseInt(String(storesResult?.connected_stores || '0')),
      };
    } catch (error) {
      logger.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }

  async getRecentActivity(userId: string, limit: number = 10): Promise<RecentActivity[]> {
    try {
      // This is a simplified version - in a real app, you'd have an activities table
      // For now, we'll generate activities from orders and designs
      
      const recentOrders = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .orderBy('orders.created_at', 'desc')
        .limit(limit / 2)
        .select('orders.id', 'orders.order_number', 'orders.customer_name', 'orders.order_status', 'orders.total_price', 'orders.created_at');

      const recentDesigns = await db('designs')
        .where('user_id', userId)
        .orderBy('created_at', 'desc')
        .limit(limit / 2)
        .select('id', 'name', 'status', 'created_at');

      const activities: RecentActivity[] = [];

      // Add order activities
      recentOrders.forEach((order: any) => {
        activities.push({
          id: `order-${order.id}`,
          type: 'order_received',
          title: 'New order received',
          description: `Order #${order.order_number} from ${order.customer_name}`,
          timestamp: order.created_at,
          metadata: {
            orderId: order.order_number,
            customerName: order.customer_name,
            amount: order.total_price
          }
        });
      });

      // Add design activities
      recentDesigns.forEach((design: any) => {
        activities.push({
          id: `design-${design.id}`,
          type: 'design_uploaded',
          title: 'New design uploaded',
          description: `${design.name} - Status: ${design.status}`,
          timestamp: design.created_at,
          metadata: {
            designName: design.name,
            status: design.status
          }
        });
      });

      // Sort by timestamp and return limited results
      return activities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      logger.error('Error fetching recent activity:', error);
      throw error;
    }
  }

  async getRevenueChartData(userId: string, from: string, to: string, interval: string) {
    try {
      const fromDate = new Date(from);
      const toDate = new Date(to);

      // Generate date series based on interval
      const dateFormat = interval === 'day' ? 'YYYY-MM-DD' : 'YYYY-MM';
      
      const revenueData = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .whereBetween('orders.created_at', [fromDate, toDate])
        .whereIn('orders.payment_status', ['paid', 'partially_paid'])
        .select(
          db.raw(`DATE_TRUNC('${interval}', orders.created_at) as date`),
          db.raw('SUM(orders.total_price) as revenue'),
          db.raw('COUNT(*) as orders'),
          db.raw('AVG(orders.total_price) as average_order_value')
        )
        .groupBy(db.raw(`DATE_TRUNC('${interval}', orders.created_at)`))
        .orderBy('date');

      return revenueData.map((item: any) => ({
        date: item.date.toISOString().split('T')[0],
        revenue: parseFloat(item.revenue || '0'),
        orders: parseInt(item.orders || '0'),
        average_order_value: parseFloat(item.average_order_value || '0')
      }));
    } catch (error) {
      logger.error('Error fetching revenue chart data:', error);
      throw error;
    }
  }

  async getOrdersTimelineData(userId: string, from: string, to: string, interval: string) {
    try {
      const fromDate = new Date(from);
      const toDate = new Date(to);

      const ordersData = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .whereBetween('orders.created_at', [fromDate, toDate])
        .select(
          db.raw(`DATE_TRUNC('${interval}', orders.created_at) as date`),
          'orders.order_status',
          db.raw('COUNT(*) as count')
        )
        .groupBy(db.raw(`DATE_TRUNC('${interval}', orders.created_at), orders.order_status`))
        .orderBy('date');

      // Transform data to the required format
      const groupedData: { [key: string]: any } = {};
      
      ordersData.forEach((item: any) => {
        const dateKey = item.date.toISOString().split('T')[0];
        if (!groupedData[dateKey]) {
          groupedData[dateKey] = {
            date: dateKey,
            pending: 0,
            approved: 0,
            processing: 0,
            shipped: 0,
            delivered: 0,
            cancelled: 0
          };
        }
        groupedData[dateKey][item.order_status] = parseInt(item.count);
      });

      return Object.values(groupedData);
    } catch (error) {
      logger.error('Error fetching orders timeline data:', error);
      throw error;
    }
  }

  async getOrdersStatusData(userId: string, from: string, to: string) {
    try {
      const fromDate = new Date(from);
      const toDate = new Date(to);

      const statusData = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .whereBetween('orders.created_at', [fromDate, toDate])
        .select('orders.order_status')
        .count('* as count')
        .groupBy('orders.order_status');

      const totalOrders = statusData.reduce((sum: number, item: any) => sum + parseInt(item.count), 0);

      return statusData.map((item: any) => ({
        status: item.order_status.charAt(0).toUpperCase() + item.order_status.slice(1),
        count: parseInt(item.count),
        percentage: Math.round((parseInt(item.count) / totalOrders) * 100)
      }));
    } catch (error) {
      logger.error('Error fetching orders status data:', error);
      throw error;
    }
  }

  async exportDashboardData(userId: string, from: string, to: string, format: string): Promise<string> {
    try {
      const fromDate = new Date(from);
      const toDate = new Date(to);

      // Get orders data for export (join with shopify_stores to filter by user)
      const orders = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .whereBetween('orders.created_at', [fromDate, toDate])
        .select('orders.*');

      if (format === 'csv') {
        // Generate CSV
        const headers = ['Order Number', 'Customer Name', 'Total Price', 'Status', 'Created At'];
        const csvRows = [headers.join(',')];

        orders.forEach((order: any) => {
          const row = [
            order.order_number,
            order.customer_name,
            order.total_price,
            order.order_status,
            order.created_at
          ];
          csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
      }

      throw new Error('Unsupported export format');
    } catch (error) {
      logger.error('Error exporting dashboard data:', error);
      throw error;
    }
  }
}
