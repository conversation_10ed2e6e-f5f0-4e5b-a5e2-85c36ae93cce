import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { jwt as jwtConfig } from '@repo/config';
import { logger } from '@/middleware/logger';
import { db } from '@/config/database';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
}

export class WebSocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, AuthenticatedSocket> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || 'http://localhost:3001',
        methods: ['GET', 'POST'],
        credentials: true
      }
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        // Verify JWT token
        const decoded = jwt.verify(token, jwtConfig.secret) as any;
        
        // Get user details from database
        const user = await db('users')
          .where('id', decoded.userId)
          .where('is_active', true)
          .first();

        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user.id;
        socket.userRole = user.role;
        
        logger.info(`WebSocket authenticated: ${user.email}`);
        next();
      } catch (error) {
        logger.error('WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      logger.info(`WebSocket connected: ${socket.userId}`);
      
      // Store connected user
      if (socket.userId) {
        this.connectedUsers.set(socket.userId, socket);
      }

      // Join user to their personal room
      socket.join(`user:${socket.userId}`);
      
      // Join admin users to admin room
      if (socket.userRole === 'admin') {
        socket.join('admin');
      }

      // Handle dashboard subscription
      socket.on('subscribe:dashboard', () => {
        socket.join('dashboard');
        logger.info(`User ${socket.userId} subscribed to dashboard updates`);
      });

      // Handle order updates subscription
      socket.on('subscribe:orders', () => {
        socket.join('orders');
        logger.info(`User ${socket.userId} subscribed to order updates`);
      });

      // Handle design updates subscription
      socket.on('subscribe:designs', () => {
        socket.join('designs');
        logger.info(`User ${socket.userId} subscribed to design updates`);
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        logger.info(`WebSocket disconnected: ${socket.userId}`);
        if (socket.userId) {
          this.connectedUsers.delete(socket.userId);
        }
      });

      // Send welcome message
      socket.emit('connected', {
        message: 'Connected to real-time updates',
        userId: socket.userId,
        timestamp: new Date().toISOString()
      });
    });
  }

  // Broadcast dashboard stats update
  public broadcastDashboardUpdate(data: any) {
    this.io.to('dashboard').emit('dashboard:stats', {
      type: 'stats_update',
      data,
      timestamp: new Date().toISOString()
    });
    logger.info('Dashboard stats broadcasted');
  }

  // Broadcast new order notification
  public broadcastNewOrder(orderData: any) {
    this.io.to('orders').emit('order:new', {
      type: 'new_order',
      data: orderData,
      timestamp: new Date().toISOString()
    });
    
    // Also send to admin room
    this.io.to('admin').emit('notification', {
      type: 'new_order',
      title: 'New Order Received',
      message: `Order #${orderData.order_number} from ${orderData.customer_name}`,
      data: orderData,
      timestamp: new Date().toISOString()
    });
    
    logger.info(`New order broadcasted: ${orderData.order_number}`);
  }

  // Broadcast order status update
  public broadcastOrderUpdate(orderData: any) {
    this.io.to('orders').emit('order:update', {
      type: 'order_update',
      data: orderData,
      timestamp: new Date().toISOString()
    });
    
    // Send to specific user
    this.io.to(`user:${orderData.user_id}`).emit('notification', {
      type: 'order_update',
      title: 'Order Status Updated',
      message: `Order #${orderData.order_number} is now ${orderData.order_status}`,
      data: orderData,
      timestamp: new Date().toISOString()
    });
    
    logger.info(`Order update broadcasted: ${orderData.order_number}`);
  }

  // Broadcast new design upload
  public broadcastNewDesign(designData: any) {
    this.io.to('designs').emit('design:new', {
      type: 'new_design',
      data: designData,
      timestamp: new Date().toISOString()
    });
    
    logger.info(`New design broadcasted: ${designData.name}`);
  }

  // Broadcast design status update
  public broadcastDesignUpdate(designData: any) {
    this.io.to('designs').emit('design:update', {
      type: 'design_update',
      data: designData,
      timestamp: new Date().toISOString()
    });
    
    // Send to specific user
    this.io.to(`user:${designData.user_id}`).emit('notification', {
      type: 'design_update',
      title: 'Design Status Updated',
      message: `Design "${designData.name}" is now ${designData.status}`,
      data: designData,
      timestamp: new Date().toISOString()
    });
    
    logger.info(`Design update broadcasted: ${designData.name}`);
  }

  // Send notification to specific user
  public sendNotificationToUser(userId: string, notification: any) {
    this.io.to(`user:${userId}`).emit('notification', {
      ...notification,
      timestamp: new Date().toISOString()
    });
    
    logger.info(`Notification sent to user ${userId}: ${notification.title}`);
  }

  // Send notification to all admin users
  public sendNotificationToAdmins(notification: any) {
    this.io.to('admin').emit('notification', {
      ...notification,
      timestamp: new Date().toISOString()
    });
    
    logger.info(`Admin notification sent: ${notification.title}`);
  }

  // Get connected users count
  public getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  // Get connected users list (admin only)
  public getConnectedUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }

  // Broadcast system maintenance message
  public broadcastSystemMessage(message: string, type: 'info' | 'warning' | 'error' = 'info') {
    this.io.emit('system:message', {
      type: 'system_message',
      level: type,
      message,
      timestamp: new Date().toISOString()
    });
    
    logger.info(`System message broadcasted: ${message}`);
  }
}
