import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { config } from '@/config';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';

export class StorageService {
  private s3Client: S3Client;
  private bucketName: string;

  constructor() {
    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: config.r2.endpoint,
      credentials: {
        accessKeyId: config.r2.accessKeyId,
        secretAccessKey: config.r2.secretAccessKey,
      },
    });
    this.bucketName = config.r2.bucketName;
  }

  /**
   * Upload file to R2 storage
   */
  async uploadFile(
    file: Buffer,
    fileName: string,
    contentType: string,
    folder: string = 'uploads'
  ): Promise<string> {
    const key = `${folder}/${uuidv4()}-${fileName}`;
    
    const command = new PutObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      Body: file,
      ContentType: contentType,
    });

    await this.s3Client.send(command);
    return `${config.r2.endpoint}/${this.bucketName}/${key}`;
  }

  /**
   * Upload design file with thumbnail generation
   */
  async uploadDesignFile(
    file: Buffer,
    fileName: string,
    contentType: string
  ): Promise<{ fileUrl: string; thumbnailUrl?: string }> {
    // Upload original file
    const fileUrl = await this.uploadFile(file, fileName, contentType, 'designs');

    let thumbnailUrl: string | undefined;

    // Generate thumbnail for images
    if (contentType.startsWith('image/')) {
      try {
        const thumbnail = await sharp(file)
          .resize(300, 300, { fit: 'inside', withoutEnlargement: true })
          .jpeg({ quality: 80 })
          .toBuffer();

        const thumbnailFileName = `thumb-${fileName.replace(/\.[^/.]+$/, '.jpg')}`;
        thumbnailUrl = await this.uploadFile(thumbnail, thumbnailFileName, 'image/jpeg', 'thumbnails');
      } catch (error) {
        console.error('Error generating thumbnail:', error);
      }
    }

    return { fileUrl, thumbnailUrl };
  }

  /**
   * Upload mockup images from URLs
   */
  async uploadMockupsFromUrls(urls: string[], designId: string): Promise<string[]> {
    const uploadPromises = urls.map(async (url, index) => {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to fetch mockup: ${response.statusText}`);
        }

        const buffer = Buffer.from(await response.arrayBuffer());
        const fileName = `mockup-${designId}-${index + 1}.jpg`;
        
        return await this.uploadFile(buffer, fileName, 'image/jpeg', 'mockups');
      } catch (error) {
        console.error(`Error uploading mockup from ${url}:`, error);
        return null;
      }
    });

    const results = await Promise.all(uploadPromises);
    return results.filter((url): url is string => url !== null);
  }

  /**
   * Get signed URL for private file access
   */
  async getSignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: key,
    });

    return await getSignedUrl(this.s3Client, command, { expiresIn });
  }

  /**
   * Delete file from storage
   */
  async deleteFile(key: string): Promise<void> {
    const command = new DeleteObjectCommand({
      Bucket: this.bucketName,
      Key: key,
    });

    await this.s3Client.send(command);
  }

  /**
   * Extract key from full URL
   */
  private extractKeyFromUrl(url: string): string {
    const urlParts = url.split('/');
    const bucketIndex = urlParts.findIndex(part => part === this.bucketName);
    return urlParts.slice(bucketIndex + 1).join('/');
  }

  /**
   * Delete file by URL
   */
  async deleteFileByUrl(url: string): Promise<void> {
    const key = this.extractKeyFromUrl(url);
    await this.deleteFile(key);
  }

  /**
   * Upload AI generated video
   */
  async uploadAIVideo(
    videoBuffer: Buffer,
    designId: string,
    format: string = 'mp4'
  ): Promise<string> {
    const fileName = `ai-video-${designId}.${format}`;
    return await this.uploadFile(videoBuffer, fileName, `video/${format}`, 'ai-videos');
  }

  /**
   * Upload AI generated mockups
   */
  async uploadAIMockups(
    mockupBuffers: Buffer[],
    designId: string
  ): Promise<string[]> {
    const uploadPromises = mockupBuffers.map(async (buffer, index) => {
      const fileName = `ai-mockup-${designId}-${index + 1}.jpg`;
      return await this.uploadFile(buffer, fileName, 'image/jpeg', 'ai-mockups');
    });

    return await Promise.all(uploadPromises);
  }

  /**
   * Batch upload files
   */
  async batchUpload(
    files: Array<{
      buffer: Buffer;
      fileName: string;
      contentType: string;
      folder?: string;
    }>
  ): Promise<string[]> {
    const uploadPromises = files.map(file =>
      this.uploadFile(file.buffer, file.fileName, file.contentType, file.folder)
    );

    return await Promise.all(uploadPromises);
  }

  /**
   * Get file info
   */
  async getFileInfo(url: string): Promise<{ size: number; lastModified: Date } | null> {
    try {
      const key = this.extractKeyFromUrl(url);
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const response = await this.s3Client.send(command);
      return {
        size: response.ContentLength || 0,
        lastModified: response.LastModified || new Date(),
      };
    } catch (error) {
      console.error('Error getting file info:', error);
      return null;
    }
  }
}
