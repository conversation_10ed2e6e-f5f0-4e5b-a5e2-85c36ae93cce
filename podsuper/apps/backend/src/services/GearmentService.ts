import axios, { AxiosInstance } from 'axios';
import crypto from 'crypto';
import { config } from '@/config';
import { Order, OrderLineItem, GearmentOrder, GearmentOrderStatus } from '@/types';

export class GearmentService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.gearment.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.gearment.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });
  }

  /**
   * Verify Gearment webhook
   */
  verifyWebhook(data: string, signature: string): boolean {
    const hmac = crypto.createHmac('sha256', config.gearment.webhookSecret);
    hmac.update(data, 'utf8');
    const calculatedSignature = hmac.digest('hex');
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(calculatedSignature, 'hex')
    );
  }

  /**
   * Create fulfillment order in Gearment
   */
  async createFulfillmentOrder(orderData: {
    external_order_id: string;
    customer: {
      email: string;
      first_name: string;
      last_name: string;
      phone?: string;
    };
    shipping_address: {
      first_name: string;
      last_name: string;
      company?: string;
      address1: string;
      address2?: string;
      city: string;
      province: string;
      country: string;
      zip: string;
      phone?: string;
    };
    line_items: Array<{
      external_line_item_id: string;
      product_id: string;
      variant_id: string;
      quantity: number;
      design_url: string;
      print_areas: Array<{
        position: string;
        design_url: string;
        x: number;
        y: number;
        scale: number;
        angle: number;
      }>;
    }>;
    notes?: string;
  }): Promise<{
    gearment_order_id: string;
    status: string;
    estimated_delivery: string;
  }> {
    try {
      const response = await this.client.post('/orders', orderData);
      return {
        gearment_order_id: response.data.id,
        status: response.data.status,
        estimated_delivery: response.data.estimated_delivery,
      };
    } catch (error) {
      console.error('Error creating fulfillment order in Gearment:', error);
      throw new Error('Failed to create fulfillment order in Gearment');
    }
  }

  /**
   * Get order status from Gearment
   */
  async getOrderStatus(gearmentOrderId: string): Promise<{
    status: GearmentOrderStatus;
    tracking_number?: string;
    tracking_url?: string;
    shipped_at?: Date;
    delivered_at?: Date;
    notes?: string;
  }> {
    try {
      const response = await this.client.get(`/orders/${gearmentOrderId}`);
      return {
        status: response.data.status,
        tracking_number: response.data.tracking_number,
        tracking_url: response.data.tracking_url,
        shipped_at: response.data.shipped_at ? new Date(response.data.shipped_at) : undefined,
        delivered_at: response.data.delivered_at ? new Date(response.data.delivered_at) : undefined,
        notes: response.data.notes,
      };
    } catch (error) {
      console.error('Error fetching order status from Gearment:', error);
      throw new Error('Failed to fetch order status from Gearment');
    }
  }

  /**
   * Cancel order in Gearment
   */
  async cancelOrder(gearmentOrderId: string, reason?: string): Promise<void> {
    try {
      await this.client.post(`/orders/${gearmentOrderId}/cancel`, {
        reason: reason || 'Customer request',
      });
    } catch (error) {
      console.error('Error cancelling order in Gearment:', error);
      throw new Error('Failed to cancel order in Gearment');
    }
  }

  /**
   * Get available products from Gearment catalog
   */
  async getProducts(): Promise<any[]> {
    try {
      const response = await this.client.get('/catalog/products');
      return response.data;
    } catch (error) {
      console.error('Error fetching products from Gearment:', error);
      throw new Error('Failed to fetch products from Gearment');
    }
  }

  /**
   * Get product details including variants and print areas
   */
  async getProductDetails(productId: string): Promise<any> {
    try {
      const response = await this.client.get(`/catalog/products/${productId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product details from Gearment:', error);
      throw new Error('Failed to fetch product details from Gearment');
    }
  }

  /**
   * Calculate shipping cost
   */
  async calculateShipping(shippingData: {
    country: string;
    province?: string;
    city?: string;
    zip?: string;
    items: Array<{
      product_id: string;
      variant_id: string;
      quantity: number;
    }>;
  }): Promise<{
    shipping_cost: number;
    currency: string;
    estimated_delivery_days: number;
  }> {
    try {
      const response = await this.client.post('/shipping/calculate', shippingData);
      return response.data;
    } catch (error) {
      console.error('Error calculating shipping cost:', error);
      throw new Error('Failed to calculate shipping cost');
    }
  }

  /**
   * Get production time estimate
   */
  async getProductionTime(items: Array<{
    product_id: string;
    variant_id: string;
    quantity: number;
  }>): Promise<{
    estimated_production_days: number;
    rush_available: boolean;
    rush_additional_cost?: number;
  }> {
    try {
      const response = await this.client.post('/production/estimate', { items });
      return response.data;
    } catch (error) {
      console.error('Error getting production time estimate:', error);
      throw new Error('Failed to get production time estimate');
    }
  }

  /**
   * Upload design file to Gearment
   */
  async uploadDesign(designData: {
    file_url: string;
    file_name: string;
    design_id: string;
  }): Promise<string> {
    try {
      const response = await this.client.post('/designs/upload', designData);
      return response.data.gearment_design_id;
    } catch (error) {
      console.error('Error uploading design to Gearment:', error);
      throw new Error('Failed to upload design to Gearment');
    }
  }

  /**
   * Get order tracking information
   */
  async getTrackingInfo(trackingNumber: string): Promise<{
    status: string;
    location: string;
    estimated_delivery: string;
    tracking_events: Array<{
      timestamp: string;
      status: string;
      location: string;
      description: string;
    }>;
  }> {
    try {
      const response = await this.client.get(`/tracking/${trackingNumber}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching tracking info:', error);
      throw new Error('Failed to fetch tracking info');
    }
  }

  /**
   * Batch create multiple orders
   */
  async batchCreateOrders(orders: Array<{
    external_order_id: string;
    customer: {
      email: string;
      first_name: string;
      last_name: string;
      phone?: string;
    };
    shipping_address: {
      first_name: string;
      last_name: string;
      company?: string;
      address1: string;
      address2?: string;
      city: string;
      province: string;
      country: string;
      zip: string;
      phone?: string;
    };
    line_items: Array<{
      external_line_item_id: string;
      product_id: string;
      variant_id: string;
      quantity: number;
      design_url: string;
      print_areas: Array<{
        position: string;
        design_url: string;
        x: number;
        y: number;
        scale: number;
        angle: number;
      }>;
    }>;
    notes?: string;
  }>): Promise<Array<{
    external_order_id: string;
    success: boolean;
    gearment_order_id?: string;
    error?: string;
  }>> {
    const results = await Promise.allSettled(
      orders.map(async (order) => {
        try {
          const result = await this.createFulfillmentOrder(order);
          return {
            external_order_id: order.external_order_id,
            success: true,
            gearment_order_id: result.gearment_order_id,
          };
        } catch (error) {
          return {
            external_order_id: order.external_order_id,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      })
    );

    return results.map((result) => 
      result.status === 'fulfilled' ? result.value : {
        external_order_id: 'unknown',
        success: false,
        error: 'Promise rejected',
      }
    );
  }

  /**
   * Get order history
   */
  async getOrderHistory(params: {
    start_date?: string;
    end_date?: string;
    status?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    orders: any[];
    total: number;
    has_more: boolean;
  }> {
    try {
      const response = await this.client.get('/orders', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching order history:', error);
      throw new Error('Failed to fetch order history');
    }
  }

  /**
   * Update order shipping address
   */
  async updateShippingAddress(gearmentOrderId: string, address: {
    first_name: string;
    last_name: string;
    company?: string;
    address1: string;
    address2?: string;
    city: string;
    province: string;
    country: string;
    zip: string;
    phone?: string;
  }): Promise<void> {
    try {
      await this.client.put(`/orders/${gearmentOrderId}/shipping-address`, { address });
    } catch (error) {
      console.error('Error updating shipping address:', error);
      throw new Error('Failed to update shipping address');
    }
  }

  /**
   * Request rush production
   */
  async requestRushProduction(gearmentOrderId: string): Promise<{
    approved: boolean;
    additional_cost: number;
    new_estimated_delivery: string;
  }> {
    try {
      const response = await this.client.post(`/orders/${gearmentOrderId}/rush`);
      return response.data;
    } catch (error) {
      console.error('Error requesting rush production:', error);
      throw new Error('Failed to request rush production');
    }
  }
}
