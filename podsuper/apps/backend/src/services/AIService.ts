import axios, { AxiosInstance } from 'axios';
import { config } from '@/config';
import { AIVideoRequest, AIVideoResponse, AIMockupRequest, AIMockupResponse } from '@/types';

export class AIService {
  private klingClient: AxiosInstance;
  private mockupClient: AxiosInstance;

  constructor() {
    this.klingClient = axios.create({
      baseURL: config.klingAI.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.klingAI.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 60000, // 60 seconds timeout
    });

    this.mockupClient = axios.create({
      baseURL: config.mockupAI.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.mockupAI.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 seconds timeout
    });
  }

  /**
   * Generate video using Kling AI
   */
  async generateVideo(request: AIVideoRequest): Promise<AIVideoResponse> {
    try {
      const response = await this.klingClient.post('/generate-video', {
        design_id: request.design_id,
        prompt: request.prompt || 'Create an engaging product showcase video',
        duration: request.duration || 15,
        style: request.style || 'modern',
        quality: 'high',
        format: 'mp4',
      });

      return {
        video_url: response.data.video_url,
        thumbnail_url: response.data.thumbnail_url,
        duration: response.data.duration,
        status: response.data.status,
      };
    } catch (error) {
      console.error('Error generating video with Kling AI:', error);
      throw new Error('Failed to generate video');
    }
  }

  /**
   * Check video generation status
   */
  async checkVideoStatus(jobId: string): Promise<AIVideoResponse> {
    try {
      const response = await this.klingClient.get(`/video-status/${jobId}`);
      return response.data;
    } catch (error) {
      console.error('Error checking video status:', error);
      throw new Error('Failed to check video status');
    }
  }

  /**
   * Generate realistic mockups using AI
   */
  async generateMockups(request: AIMockupRequest): Promise<AIMockupResponse> {
    try {
      const response = await this.mockupClient.post('/generate-mockups', {
        design_id: request.design_id,
        product_type: request.product_type,
        style: request.style || 'realistic',
        background: request.background || 'lifestyle',
        count: 3, // Generate 3 mockups by default
        quality: 'high',
        format: 'jpg',
      });

      return {
        mockup_urls: response.data.mockup_urls,
        status: response.data.status,
      };
    } catch (error) {
      console.error('Error generating mockups with AI:', error);
      throw new Error('Failed to generate mockups');
    }
  }

  /**
   * Check mockup generation status
   */
  async checkMockupStatus(jobId: string): Promise<AIMockupResponse> {
    try {
      const response = await this.mockupClient.get(`/mockup-status/${jobId}`);
      return response.data;
    } catch (error) {
      console.error('Error checking mockup status:', error);
      throw new Error('Failed to check mockup status');
    }
  }

  /**
   * Generate product description using AI
   */
  async generateProductDescription(designName: string, tags: string[]): Promise<string> {
    try {
      const response = await this.mockupClient.post('/generate-description', {
        product_name: designName,
        tags: tags,
        style: 'marketing',
        length: 'medium',
      });

      return response.data.description;
    } catch (error) {
      console.error('Error generating product description:', error);
      return `Beautiful ${designName} design perfect for your style. High-quality print with vibrant colors.`;
    }
  }

  /**
   * Generate SEO-friendly product title
   */
  async generateProductTitle(designName: string, productType: string): Promise<string> {
    try {
      const response = await this.mockupClient.post('/generate-title', {
        design_name: designName,
        product_type: productType,
        style: 'seo-friendly',
      });

      return response.data.title;
    } catch (error) {
      console.error('Error generating product title:', error);
      return `${designName} - Premium ${productType}`;
    }
  }

  /**
   * Generate product tags using AI
   */
  async generateProductTags(designName: string, description: string): Promise<string[]> {
    try {
      const response = await this.mockupClient.post('/generate-tags', {
        design_name: designName,
        description: description,
        max_tags: 10,
      });

      return response.data.tags;
    } catch (error) {
      console.error('Error generating product tags:', error);
      return [designName.toLowerCase(), 'custom', 'design', 'print', 'quality'];
    }
  }

  /**
   * Enhance design with AI filters/effects
   */
  async enhanceDesign(designUrl: string, enhancement: string): Promise<string> {
    try {
      const response = await this.mockupClient.post('/enhance-design', {
        design_url: designUrl,
        enhancement: enhancement,
        quality: 'high',
      });

      return response.data.enhanced_url;
    } catch (error) {
      console.error('Error enhancing design:', error);
      throw new Error('Failed to enhance design');
    }
  }

  /**
   * Generate color variations of a design
   */
  async generateColorVariations(designUrl: string, colors: string[]): Promise<string[]> {
    try {
      const response = await this.mockupClient.post('/generate-variations', {
        design_url: designUrl,
        colors: colors,
        format: 'png',
      });

      return response.data.variation_urls;
    } catch (error) {
      console.error('Error generating color variations:', error);
      throw new Error('Failed to generate color variations');
    }
  }

  /**
   * Analyze design for optimal product placement
   */
  async analyzeDesign(designUrl: string): Promise<{
    recommended_products: string[];
    color_palette: string[];
    style_tags: string[];
    quality_score: number;
  }> {
    try {
      const response = await this.mockupClient.post('/analyze-design', {
        design_url: designUrl,
      });

      return response.data;
    } catch (error) {
      console.error('Error analyzing design:', error);
      return {
        recommended_products: ['t-shirt', 'hoodie', 'mug'],
        color_palette: ['#000000', '#FFFFFF'],
        style_tags: ['modern', 'minimalist'],
        quality_score: 8.0,
      };
    }
  }

  /**
   * Batch process multiple AI operations
   */
  async batchProcess(operations: Array<{
    type: 'video' | 'mockup' | 'enhance';
    data: any;
  }>): Promise<any[]> {
    const promises = operations.map(async (operation) => {
      try {
        switch (operation.type) {
          case 'video':
            return await this.generateVideo(operation.data);
          case 'mockup':
            return await this.generateMockups(operation.data);
          case 'enhance':
            return await this.enhanceDesign(operation.data.designUrl, operation.data.enhancement);
          default:
            throw new Error(`Unknown operation type: ${operation.type}`);
        }
      } catch (error) {
        console.error(`Error in batch operation ${operation.type}:`, error);
        return null;
      }
    });

    return await Promise.all(promises);
  }
}
