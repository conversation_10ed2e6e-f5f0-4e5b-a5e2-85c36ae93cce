import axios, { AxiosInstance } from 'axios';
import crypto from 'crypto';
import { config } from '@/config';
import { ShopifyStore, ShopifyProduct, Order } from '@/types';

export class ShopifyService {
  /**
   * Create Shopify client for a specific store
   */
  private createClient(store: ShopifyStore): AxiosInstance {
    return axios.create({
      baseURL: `https://${store.shop_domain}/admin/api/2023-10`,
      headers: {
        'X-Shopify-Access-Token': store.access_token,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });
  }

  /**
   * Verify Shopify webhook
   */
  verifyWebhook(data: string, signature: string): boolean {
    const hmac = crypto.createHmac('sha256', config.shopify.webhookSecret);
    hmac.update(data, 'utf8');
    const calculatedSignature = hmac.digest('base64');
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'base64'),
      Buffer.from(calculatedSignature, 'base64')
    );
  }

  /**
   * Create or update product on Shopify
   */
  async createProduct(store: ShopifyStore, productData: {
    title: string;
    body_html: string;
    vendor: string;
    product_type: string;
    tags: string;
    images: Array<{
      src: string;
      alt?: string;
    }>;
    variants: Array<{
      title: string;
      price: string;
      sku: string;
      inventory_quantity: number;
      weight: number;
      weight_unit: string;
    }>;
    options: Array<{
      name: string;
      values: string[];
    }>;
  }): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.post('/products.json', {
        product: productData,
      });
      return response.data.product;
    } catch (error) {
      console.error('Error creating product on Shopify:', error);
      throw new Error('Failed to create product on Shopify');
    }
  }

  /**
   * Update product on Shopify
   */
  async updateProduct(store: ShopifyStore, productId: string, updateData: any): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.put(`/products/${productId}.json`, {
        product: updateData,
      });
      return response.data.product;
    } catch (error) {
      console.error('Error updating product on Shopify:', error);
      throw new Error('Failed to update product on Shopify');
    }
  }

  /**
   * Get product from Shopify
   */
  async getProduct(store: ShopifyStore, productId: string): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.get(`/products/${productId}.json`);
      return response.data.product;
    } catch (error) {
      console.error('Error fetching product from Shopify:', error);
      throw new Error('Failed to fetch product from Shopify');
    }
  }

  /**
   * Delete product from Shopify
   */
  async deleteProduct(store: ShopifyStore, productId: string): Promise<void> {
    try {
      const client = this.createClient(store);
      await client.delete(`/products/${productId}.json`);
    } catch (error) {
      console.error('Error deleting product from Shopify:', error);
      throw new Error('Failed to delete product from Shopify');
    }
  }

  /**
   * Get orders from Shopify
   */
  async getOrders(store: ShopifyStore, params: {
    status?: string;
    limit?: number;
    since_id?: string;
    created_at_min?: string;
    created_at_max?: string;
  } = {}): Promise<any[]> {
    try {
      const client = this.createClient(store);
      const response = await client.get('/orders.json', { params });
      return response.data.orders;
    } catch (error) {
      console.error('Error fetching orders from Shopify:', error);
      throw new Error('Failed to fetch orders from Shopify');
    }
  }

  /**
   * Get specific order from Shopify
   */
  async getOrder(store: ShopifyStore, orderId: string): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.get(`/orders/${orderId}.json`);
      return response.data.order;
    } catch (error) {
      console.error('Error fetching order from Shopify:', error);
      throw new Error('Failed to fetch order from Shopify');
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(store: ShopifyStore, orderId: string, status: string): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.put(`/orders/${orderId}.json`, {
        order: {
          id: orderId,
          tags: status,
        },
      });
      return response.data.order;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw new Error('Failed to update order status');
    }
  }

  /**
   * Create fulfillment for order
   */
  async createFulfillment(store: ShopifyStore, orderId: string, fulfillmentData: {
    line_items: Array<{
      id: string;
      quantity: number;
    }>;
    tracking_number?: string;
    tracking_company?: string;
    tracking_url?: string;
    notify_customer?: boolean;
  }): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.post(`/orders/${orderId}/fulfillments.json`, {
        fulfillment: fulfillmentData,
      });
      return response.data.fulfillment;
    } catch (error) {
      console.error('Error creating fulfillment:', error);
      throw new Error('Failed to create fulfillment');
    }
  }

  /**
   * Update fulfillment tracking
   */
  async updateFulfillmentTracking(
    store: ShopifyStore,
    orderId: string,
    fulfillmentId: string,
    trackingData: {
      tracking_number: string;
      tracking_company?: string;
      tracking_url?: string;
    }
  ): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.put(`/orders/${orderId}/fulfillments/${fulfillmentId}.json`, {
        fulfillment: trackingData,
      });
      return response.data.fulfillment;
    } catch (error) {
      console.error('Error updating fulfillment tracking:', error);
      throw new Error('Failed to update fulfillment tracking');
    }
  }

  /**
   * Create webhook
   */
  async createWebhook(store: ShopifyStore, webhookData: {
    topic: string;
    address: string;
    format?: string;
  }): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.post('/webhooks.json', {
        webhook: {
          ...webhookData,
          format: webhookData.format || 'json',
        },
      });
      return response.data.webhook;
    } catch (error) {
      console.error('Error creating webhook:', error);
      throw new Error('Failed to create webhook');
    }
  }

  /**
   * Get webhooks
   */
  async getWebhooks(store: ShopifyStore): Promise<any[]> {
    try {
      const client = this.createClient(store);
      const response = await client.get('/webhooks.json');
      return response.data.webhooks;
    } catch (error) {
      console.error('Error fetching webhooks:', error);
      throw new Error('Failed to fetch webhooks');
    }
  }

  /**
   * Setup required webhooks for the store
   */
  async setupWebhooks(store: ShopifyStore, baseUrl: string): Promise<void> {
    const requiredWebhooks = [
      {
        topic: 'orders/create',
        address: `${baseUrl}/webhooks/shopify/orders/create`,
      },
      {
        topic: 'orders/updated',
        address: `${baseUrl}/webhooks/shopify/orders/updated`,
      },
      {
        topic: 'orders/paid',
        address: `${baseUrl}/webhooks/shopify/orders/paid`,
      },
      {
        topic: 'orders/cancelled',
        address: `${baseUrl}/webhooks/shopify/orders/cancelled`,
      },
    ];

    try {
      const existingWebhooks = await this.getWebhooks(store);
      
      for (const webhook of requiredWebhooks) {
        const exists = existingWebhooks.some(
          (existing) => existing.topic === webhook.topic && existing.address === webhook.address
        );
        
        if (!exists) {
          await this.createWebhook(store, webhook);
          console.log(`Created webhook for ${webhook.topic}`);
        }
      }
    } catch (error) {
      console.error('Error setting up webhooks:', error);
      throw new Error('Failed to setup webhooks');
    }
  }

  /**
   * Get store information
   */
  async getStoreInfo(store: ShopifyStore): Promise<any> {
    try {
      const client = this.createClient(store);
      const response = await client.get('/shop.json');
      return response.data.shop;
    } catch (error) {
      console.error('Error fetching store info:', error);
      throw new Error('Failed to fetch store info');
    }
  }

  /**
   * Batch create products
   */
  async batchCreateProducts(
    store: ShopifyStore,
    products: Array<{
      title: string;
      body_html: string;
      vendor: string;
      product_type: string;
      tags: string;
      images: Array<{ src: string; alt?: string }>;
      variants: Array<{
        title: string;
        price: string;
        sku: string;
        inventory_quantity: number;
        weight: number;
        weight_unit: string;
      }>;
      options: Array<{ name: string; values: string[] }>;
    }>
  ): Promise<Array<{ success: boolean; product?: any; error?: string }>> {
    const results = await Promise.allSettled(
      products.map((product) => this.createProduct(store, product))
    );

    return results.map((result) => 
      result.status === 'fulfilled'
        ? { success: true, product: result.value }
        : { success: false, error: result.reason?.message || 'Unknown error' }
    );
  }
}
