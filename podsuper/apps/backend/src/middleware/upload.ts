import multer from 'multer';
import { config } from '@/config';

// Configure multer for memory storage
const storage = multer.memoryStorage();

// File filter function
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check file type
  if (!config.upload.allowedFileTypes.includes(file.mimetype)) {
    cb(new Error(`File type ${file.mimetype} not allowed. Allowed types: ${config.upload.allowedFileTypes.join(', ')}`));
    return;
  }

  cb(null, true);
};

// Create multer instance
export const uploadMiddleware = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 1, // Single file upload
  },
});

// Multiple files upload middleware
export const uploadMultipleMiddleware = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 10, // Maximum 10 files
  },
});

// Error handling middleware for multer
export const handleUploadError = (error: any, req: any, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: `File too large. Maximum size is ${config.upload.maxFileSize} bytes`,
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files uploaded',
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        error: 'Unexpected file field',
      });
    }
  }

  if (error.message.includes('File type')) {
    return res.status(400).json({
      success: false,
      error: error.message,
    });
  }

  next(error);
};
