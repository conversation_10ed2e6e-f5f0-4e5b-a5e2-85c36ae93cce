import { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { db } from '@/config/database';
import { logger } from '@/middleware/logger';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'designs');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `design-${uniqueSuffix}${ext}`);
  }
});

const fileFilter = (req: any, file: any, cb: any) => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images are allowed.'), false);
  }
};

export const uploadMiddleware = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

export class DesignUploadController {
  // Upload design
  async uploadDesign(req: Request, res: Response) {
    try {
      const userId = req.user?.id;
      const { name, description, category, tags, price, is_public } = req.body;
      const file = req.file;

      if (!file) {
        return res.status(400).json({
          success: false,
          error: 'No file uploaded'
        });
      }

      if (!name || !category) {
        return res.status(400).json({
          success: false,
          error: 'Name and category are required'
        });
      }

      // Create design record
      const [design] = await db('designs')
        .insert({
          id: db.raw('gen_random_uuid()'),
          user_id: userId,
          name,
          description: description || null,
          category,
          tags: tags || null,
          price: parseFloat(price) || 0,
          is_public: is_public === 'true',
          file_path: file.path,
          file_name: file.filename,
          file_size: file.size,
          mime_type: file.mimetype,
          status: 'pending', // pending, approved, rejected
          created_at: new Date(),
          updated_at: new Date()
        })
        .returning('*');

      // Broadcast design upload event via WebSocket
      if (global.wsService) {
        global.wsService.broadcastNewDesign({
          designId: design.id,
          name: design.name,
          category: design.category,
          userId: design.user_id
        });

        // Notify admins
        global.wsService.sendNotificationToAdmins({
          type: 'design_upload',
          title: 'New Design Uploaded',
          message: `${name} has been uploaded and is pending review`,
          data: { designId: design.id }
        });
      }

      logger.info(`Design uploaded: ${design.name} by user ${userId}`);

      res.status(201).json({
        success: true,
        data: design,
        message: 'Design uploaded successfully'
      });
    } catch (error) {
      logger.error('Upload design error:', error);
      
      // Clean up uploaded file if database insert failed
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        success: false,
        error: 'Failed to upload design'
      });
    }
  }

  // Get user designs
  async getUserDesigns(req: Request, res: Response) {
    try {
      const userId = req.user?.id;
      const { status, category, limit = 20, offset = 0 } = req.query;

      let query = db('designs')
        .where('user_id', userId)
        .orderBy('created_at', 'desc')
        .limit(Number(limit))
        .offset(Number(offset));

      if (status) {
        query = query.where('status', status);
      }

      if (category) {
        query = query.where('category', category);
      }

      const designs = await query;

      res.json({
        success: true,
        data: designs
      });
    } catch (error) {
      logger.error('Get user designs error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch designs'
      });
    }
  }

  // Get all designs (admin only)
  async getAllDesigns(req: Request, res: Response) {
    try {
      const { status, category, limit = 50, offset = 0 } = req.query;

      let query = db('designs')
        .select(
          'designs.*',
          'users.first_name',
          'users.last_name',
          'users.email'
        )
        .leftJoin('users', 'designs.user_id', 'users.id')
        .orderBy('designs.created_at', 'desc')
        .limit(Number(limit))
        .offset(Number(offset));

      if (status) {
        query = query.where('designs.status', status);
      }

      if (category) {
        query = query.where('designs.category', category);
      }

      const designs = await query;

      res.json({
        success: true,
        data: designs
      });
    } catch (error) {
      logger.error('Get all designs error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch designs'
      });
    }
  }

  // Update design status (admin only)
  async updateDesignStatus(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { status, rejection_reason } = req.body;

      if (!['pending', 'approved', 'rejected'].includes(status)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid status'
        });
      }

      const [updatedDesign] = await db('designs')
        .where('id', id)
        .update({
          status,
          rejection_reason: status === 'rejected' ? rejection_reason : null,
          reviewed_at: new Date(),
          reviewed_by: req.user?.id,
          updated_at: new Date()
        })
        .returning('*');

      if (!updatedDesign) {
        return res.status(404).json({
          success: false,
          error: 'Design not found'
        });
      }

      // Broadcast design status update via WebSocket
      if (global.wsService) {
        global.wsService.broadcastDesignUpdate({
          designId: updatedDesign.id,
          name: updatedDesign.name,
          status: updatedDesign.status,
          user_id: updatedDesign.user_id
        });
      }

      logger.info(`Design status updated: ${updatedDesign.name} - ${status}`);

      res.json({
        success: true,
        data: updatedDesign,
        message: `Design ${status} successfully`
      });
    } catch (error) {
      logger.error('Update design status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update design status'
      });
    }
  }

  // Delete design
  async deleteDesign(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      const userRole = req.user?.role;

      // Get design details
      const design = await db('designs').where('id', id).first();

      if (!design) {
        return res.status(404).json({
          success: false,
          error: 'Design not found'
        });
      }

      // Check permissions (owner or admin)
      if (design.user_id !== userId && userRole !== 'admin') {
        return res.status(403).json({
          success: false,
          error: 'Permission denied'
        });
      }

      // Delete file from filesystem
      if (design.file_path && fs.existsSync(design.file_path)) {
        fs.unlinkSync(design.file_path);
      }

      // Delete from database
      await db('designs').where('id', id).del();

      logger.info(`Design deleted: ${design.name}`);

      res.json({
        success: true,
        message: 'Design deleted successfully'
      });
    } catch (error) {
      logger.error('Delete design error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete design'
      });
    }
  }

  // Get design file
  async getDesignFile(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const design = await db('designs').where('id', id).first();

      if (!design) {
        return res.status(404).json({
          success: false,
          error: 'Design not found'
        });
      }

      // Check if file exists
      if (!design.file_path || !fs.existsSync(design.file_path)) {
        return res.status(404).json({
          success: false,
          error: 'Design file not found'
        });
      }

      // Set appropriate headers
      res.setHeader('Content-Type', design.mime_type);
      res.setHeader('Content-Disposition', `inline; filename="${design.file_name}"`);

      // Stream file
      const fileStream = fs.createReadStream(design.file_path);
      fileStream.pipe(res);
    } catch (error) {
      logger.error('Get design file error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get design file'
      });
    }
  }

  // Get design categories
  async getCategories(req: Request, res: Response) {
    try {
      const categories = [
        { value: 't-shirts', label: 'T-Shirts', count: 45 },
        { value: 'hoodies', label: 'Hoodies', count: 23 },
        { value: 'mugs', label: 'Mugs', count: 67 },
        { value: 'stickers', label: 'Stickers', count: 89 },
        { value: 'posters', label: 'Posters', count: 12 },
        { value: 'other', label: 'Other', count: 8 }
      ];

      res.json({
        success: true,
        data: categories
      });
    } catch (error) {
      logger.error('Get categories error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch categories'
      });
    }
  }
}
