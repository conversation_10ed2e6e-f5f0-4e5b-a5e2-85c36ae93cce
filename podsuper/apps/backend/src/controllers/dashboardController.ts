import { Request, Response } from 'express';
import { DashboardService } from '@/services/dashboardService';
import { logger } from '@/middleware/logger';

export class DashboardController {
  private dashboardService: DashboardService;

  constructor() {
    this.dashboardService = new DashboardService();
  }

  getStats = async (req: Request, res: Response) => {
    try {
      const { from, to } = req.query;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      const stats = await this.dashboardService.getDashboardStats(
        userId,
        from as string,
        to as string
      );

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error fetching dashboard stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch dashboard stats'
      });
    }
  };

  getRecentActivity = async (req: Request, res: Response) => {
    try {
      const { limit = 10 } = req.query;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      const activities = await this.dashboardService.getRecentActivity(
        userId,
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: activities
      });
    } catch (error) {
      logger.error('Error fetching recent activity:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch recent activity'
      });
    }
  };

  getRevenueChart = async (req: Request, res: Response) => {
    try {
      const { from, to, interval = 'day' } = req.query;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      const revenueData = await this.dashboardService.getRevenueChartData(
        userId,
        from as string,
        to as string,
        interval as string
      );

      res.json({
        success: true,
        data: revenueData
      });
    } catch (error) {
      logger.error('Error fetching revenue chart data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch revenue chart data'
      });
    }
  };

  getOrdersTimeline = async (req: Request, res: Response) => {
    try {
      const { from, to, interval = 'day' } = req.query;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      const ordersData = await this.dashboardService.getOrdersTimelineData(
        userId,
        from as string,
        to as string,
        interval as string
      );

      res.json({
        success: true,
        data: ordersData
      });
    } catch (error) {
      logger.error('Error fetching orders timeline data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch orders timeline data'
      });
    }
  };

  getOrdersStatus = async (req: Request, res: Response) => {
    try {
      const { from, to } = req.query;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      // Default to last 30 days if no dates provided
      const defaultFrom = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
      const defaultTo = new Date().toISOString();

      const statusData = await this.dashboardService.getOrdersStatusData(
        userId,
        (from as string) || defaultFrom,
        (to as string) || defaultTo
      );

      res.json({
        success: true,
        data: statusData
      });
    } catch (error) {
      logger.error('Error fetching orders status data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch orders status data'
      });
    }
  };

  exportData = async (req: Request, res: Response) => {
    try {
      const { from, to, format = 'csv' } = req.query;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      const exportData = await this.dashboardService.exportDashboardData(
        userId,
        from as string,
        to as string,
        format as string
      );

      // Set appropriate headers for file download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=dashboard-export-${Date.now()}.csv`);
      
      res.send(exportData);
    } catch (error) {
      logger.error('Error exporting dashboard data:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export dashboard data'
      });
    }
  };
}
