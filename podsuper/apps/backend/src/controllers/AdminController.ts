import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { db } from '@/config/database';
import { logger } from '@/middleware/logger';

export class AdminController {
  // Get all users with stats
  async getUsers(req: Request, res: Response) {
    try {
      const users = await db('users')
        .select(
          'users.*',
          db.raw('COALESCE(order_stats.total_orders, 0) as total_orders'),
          db.raw('COALESCE(order_stats.total_revenue, 0) as total_revenue'),
          db.raw('order_stats.last_order_date')
        )
        .leftJoin(
          db('orders')
            .select(
              'user_id',
              db.raw('COUNT(*) as total_orders'),
              db.raw('SUM(total_amount) as total_revenue'),
              db.raw('MAX(created_at) as last_order_date')
            )
            .groupBy('user_id')
            .as('order_stats'),
          'users.id',
          'order_stats.user_id'
        )
        .orderBy('users.created_at', 'desc');

      // Add status based on activity
      const usersWithStatus = users.map(user => ({
        ...user,
        status: user.is_active 
          ? (user.last_login_at && new Date(user.last_login_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) 
              ? 'active' 
              : 'inactive')
          : 'suspended'
      }));

      res.json({
        success: true,
        data: usersWithStatus
      });
    } catch (error) {
      logger.error('Get users error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch users'
      });
    }
  }

  // Create new user
  async createUser(req: Request, res: Response) {
    try {
      const { first_name, last_name, email, role, is_active } = req.body;

      // Check if user already exists
      const existingUser = await db('users')
        .where('email', email.toLowerCase())
        .first();

      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: 'User with this email already exists'
        });
      }

      // Generate temporary password
      const tempPassword = Math.random().toString(36).slice(-8);
      const hashedPassword = await bcrypt.hash(tempPassword, 10);

      // Create user
      const [newUser] = await db('users')
        .insert({
          id: db.raw('gen_random_uuid()'),
          email: email.toLowerCase(),
          password_hash: hashedPassword,
          first_name,
          last_name,
          role: role || 'user',
          is_active: is_active !== undefined ? is_active : true,
          created_at: new Date(),
          updated_at: new Date()
        })
        .returning('*');

      // Remove password from response
      const { password_hash: _, ...userWithoutPassword } = newUser;

      logger.info(`Admin created user: ${newUser.email}`);

      res.status(201).json({
        success: true,
        data: {
          user: userWithoutPassword,
          tempPassword // In production, send this via email
        },
        message: 'User created successfully'
      });
    } catch (error) {
      logger.error('Create user error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create user'
      });
    }
  }

  // Update user
  async updateUser(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { first_name, last_name, email, role, is_active } = req.body;

      // Check if user exists
      const existingUser = await db('users').where('id', id).first();
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Check if email is already taken by another user
      if (email && email !== existingUser.email) {
        const emailExists = await db('users')
          .where('email', email.toLowerCase())
          .where('id', '!=', id)
          .first();

        if (emailExists) {
          return res.status(409).json({
            success: false,
            error: 'Email already taken by another user'
          });
        }
      }

      // Update user
      const [updatedUser] = await db('users')
        .where('id', id)
        .update({
          first_name: first_name || existingUser.first_name,
          last_name: last_name || existingUser.last_name,
          email: email ? email.toLowerCase() : existingUser.email,
          role: role || existingUser.role,
          is_active: is_active !== undefined ? is_active : existingUser.is_active,
          updated_at: new Date()
        })
        .returning('*');

      // Remove password from response
      const { password_hash: _, ...userWithoutPassword } = updatedUser;

      logger.info(`Admin updated user: ${updatedUser.email}`);

      res.json({
        success: true,
        data: userWithoutPassword,
        message: 'User updated successfully'
      });
    } catch (error) {
      logger.error('Update user error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update user'
      });
    }
  }

  // Delete user
  async deleteUser(req: Request, res: Response) {
    try {
      const { id } = req.params;

      // Check if user exists
      const existingUser = await db('users').where('id', id).first();
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Prevent deleting admin users (safety check)
      if (existingUser.role === 'admin') {
        return res.status(403).json({
          success: false,
          error: 'Cannot delete admin users'
        });
      }

      // Delete user
      await db('users').where('id', id).del();

      logger.info(`Admin deleted user: ${existingUser.email}`);

      res.json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      logger.error('Delete user error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete user'
      });
    }
  }

  // Toggle user status
  async toggleUserStatus(req: Request, res: Response) {
    try {
      const { id } = req.params;

      // Check if user exists
      const existingUser = await db('users').where('id', id).first();
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      // Toggle status
      const [updatedUser] = await db('users')
        .where('id', id)
        .update({
          is_active: !existingUser.is_active,
          updated_at: new Date()
        })
        .returning('*');

      // Remove password from response
      const { password_hash: _, ...userWithoutPassword } = updatedUser;

      logger.info(`Admin toggled user status: ${updatedUser.email} - ${updatedUser.is_active ? 'activated' : 'deactivated'}`);

      res.json({
        success: true,
        data: userWithoutPassword,
        message: `User ${updatedUser.is_active ? 'activated' : 'deactivated'} successfully`
      });
    } catch (error) {
      logger.error('Toggle user status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to toggle user status'
      });
    }
  }

  // Get advanced analytics
  async getAdvancedAnalytics(req: Request, res: Response) {
    try {
      const { range = '30d' } = req.query;
      
      // Calculate date range
      let startDate: Date;
      switch (range) {
        case '7d':
          startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
          break;
        case '1y':
          startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
          break;
        default: // 30d
          startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      }

      // Generate mock analytics data for demo
      const analytics = {
        revenue: {
          total: 125430,
          growth: 18.5,
          forecast: 145000,
          breakdown: Array.from({ length: 30 }, (_, i) => ({
            period: `Day ${i + 1}`,
            revenue: Math.random() * 5000 + 2000,
            orders: Math.floor(Math.random() * 50 + 20),
            aov: Math.random() * 100 + 50,
            conversion: Math.random() * 5 + 2
          }))
        },
        customers: {
          total: 2847,
          new: 342,
          returning: 2505,
          churn: 5.2,
          lifetime_value: 287.50,
          segments: [
            { segment: 'VIP', count: 156, value: 15420, growth: 12.3 },
            { segment: 'Regular', count: 1890, value: 78450, growth: 8.7 },
            { segment: 'New', count: 801, value: 31560, growth: 25.1 }
          ]
        },
        products: {
          total_designs: 234,
          active_designs: 189,
          top_performers: [
            { design_id: '1', name: 'Summer Vibes T-Shirt', revenue: 12450, orders: 234, conversion: 8.5 },
            { design_id: '2', name: 'Vintage Logo Hoodie', revenue: 9870, orders: 156, conversion: 6.2 },
            { design_id: '3', name: 'Minimalist Mug', revenue: 7650, orders: 345, conversion: 12.1 }
          ],
          categories: [
            { category: 'T-Shirts', revenue: 45230, orders: 892, growth: 15.2 },
            { category: 'Hoodies', revenue: 32100, orders: 234, growth: 8.7 },
            { category: 'Mugs', revenue: 18900, orders: 567, growth: 22.1 },
            { category: 'Stickers', revenue: 12400, orders: 1234, growth: 5.3 }
          ]
        },
        performance: {
          conversion_rate: 7.8,
          avg_order_value: 67.50,
          fulfillment_time: 3.2,
          customer_satisfaction: 4.6,
          return_rate: 2.1,
          profit_margin: 34.5
        }
      };

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Get advanced analytics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch analytics'
      });
    }
  }
}
