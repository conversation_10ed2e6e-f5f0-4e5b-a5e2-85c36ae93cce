import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { db } from '@/config/database';
import { generateToken } from '@/middleware/auth';
import { logger } from '@/middleware/logger';
import { LoginCredentials, RegisterData, User } from '@repo/types';

export class AuthController {
  async login(req: Request, res: Response) {
    try {
      const { email, password }: LoginCredentials = req.body;

      // Validate input
      if (!email || !password) {
        return res.status(400).json({
          success: false,
          error: 'Email and password are required'
        });
      }

      // Find user by email
      const user = await db('users')
        .where('email', email.toLowerCase())
        .where('is_active', true)
        .first();

      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        });
      }

      // Check password
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        });
      }

      // Update last login
      await db('users')
        .where('id', user.id)
        .update({
          last_login_at: new Date(),
          updated_at: new Date()
        });

      // Generate JWT token
      const token = generateToken(user.id);

      // Remove password from user object
      const { password_hash: _, ...userWithoutPassword } = user;

      logger.info(`User logged in: ${user.email}`);

      res.json({
        success: true,
        data: {
          user: userWithoutPassword,
          token
        },
        message: 'Login successful'
      });
    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  async register(req: Request, res: Response) {
    try {
      const { email, password, first_name, last_name }: RegisterData = req.body;

      // Validate input
      if (!email || !password || !first_name || !last_name) {
        return res.status(400).json({
          success: false,
          error: 'All fields are required'
        });
      }

      // Check if user already exists
      const existingUser = await db('users')
        .where('email', email.toLowerCase())
        .first();

      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: 'User with this email already exists'
        });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const [newUser] = await db('users')
        .insert({
          id: db.raw('gen_random_uuid()'),
          email: email.toLowerCase(),
          password_hash: hashedPassword,
          first_name,
          last_name,
          role: 'user',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        })
        .returning('*');

      // Generate JWT token
      const token = generateToken(newUser.id);

      // Remove password from user object
      const { password_hash: _, ...userWithoutPassword } = newUser;

      logger.info(`New user registered: ${newUser.email}`);

      res.status(201).json({
        success: true,
        data: {
          user: userWithoutPassword,
          token
        },
        message: 'Registration successful'
      });
    } catch (error) {
      logger.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  async me(req: Request, res: Response) {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      // Get user details
      const user = await db('users')
        .where('id', userId)
        .where('is_active', true)
        .select('id', 'email', 'first_name', 'last_name', 'role', 'is_active', 'email_verified_at', 'last_login_at', 'created_at', 'updated_at')
        .first();

      if (!user) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      logger.error('Get user profile error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  async logout(req: Request, res: Response) {
    try {
      // In a JWT-based system, logout is typically handled client-side
      // by removing the token from storage
      res.json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      logger.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  async refreshToken(req: Request, res: Response) {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'User not authenticated'
        });
      }

      // Generate new token
      const token = generateToken(userId);

      res.json({
        success: true,
        data: { token },
        message: 'Token refreshed successfully'
      });
    } catch (error) {
      logger.error('Token refresh error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}
