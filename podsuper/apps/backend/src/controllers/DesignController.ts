import { Request, Response } from 'express';
import { StorageService } from '@/services/StorageService';
import { AIService } from '@/services/AIService';
import { PrintifyService } from '@/services/PrintifyService';
import { Design, DesignStatus, ApiResponse } from '@/types';
import knex from 'knex';
import { config } from '@/config';
import { v4 as uuidv4 } from 'uuid';

const db = knex(require('@/config/database').default.development);

export class DesignController {
  private storageService: StorageService;
  private aiService: AIService;
  private printifyService: PrintifyService;

  constructor() {
    this.storageService = new StorageService();
    this.aiService = new AIService();
    this.printifyService = new PrintifyService();
  }

  /**
   * Upload and create new design
   */
  async createDesign(req: Request, res: Response): Promise<void> {
    try {
      const { name, description, tags } = req.body;
      const file = req.file;
      const userId = req.user?.id;

      if (!file) {
        res.status(400).json({
          success: false,
          error: 'No file uploaded',
        });
        return;
      }

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'User not authenticated',
        });
        return;
      }

      // Upload file to storage
      const { fileUrl, thumbnailUrl } = await this.storageService.uploadDesignFile(
        file.buffer,
        file.originalname,
        file.mimetype
      );

      // Create design record
      const designId = uuidv4();
      const design = await db('designs').insert({
        id: designId,
        name,
        description,
        file_url: fileUrl,
        file_type: file.mimetype,
        file_size: file.size,
        thumbnail_url: thumbnailUrl,
        tags: JSON.stringify(tags || []),
        status: DesignStatus.DRAFT,
        user_id: userId,
      }).returning('*');

      res.status(201).json({
        success: true,
        data: design[0],
        message: 'Design created successfully',
      });
    } catch (error) {
      console.error('Error creating design:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create design',
      });
    }
  }

  /**
   * Get all designs for user
   */
  async getDesigns(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const { page = 1, limit = 20, status, search } = req.query;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'User not authenticated',
        });
        return;
      }

      let query = db('designs').where('user_id', userId);

      if (status) {
        query = query.where('status', status as string);
      }

      if (search) {
        query = query.where('name', 'ilike', `%${search}%`);
      }

      const offset = (Number(page) - 1) * Number(limit);
      const designs = await query
        .orderBy('created_at', 'desc')
        .limit(Number(limit))
        .offset(offset);

      const total = await db('designs')
        .where('user_id', userId)
        .count('* as count')
        .first();

      res.json({
        success: true,
        data: designs,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: Number(total?.count || 0),
          pages: Math.ceil(Number(total?.count || 0) / Number(limit)),
        },
      });
    } catch (error) {
      console.error('Error fetching designs:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch designs',
      });
    }
  }

  /**
   * Get single design
   */
  async getDesign(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const design = await db('designs')
        .where({ id, user_id: userId })
        .first();

      if (!design) {
        res.status(404).json({
          success: false,
          error: 'Design not found',
        });
        return;
      }

      res.json({
        success: true,
        data: design,
      });
    } catch (error) {
      console.error('Error fetching design:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch design',
      });
    }
  }

  /**
   * Update design
   */
  async updateDesign(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { name, description, tags, status } = req.body;
      const userId = req.user?.id;

      const design = await db('designs')
        .where({ id, user_id: userId })
        .first();

      if (!design) {
        res.status(404).json({
          success: false,
          error: 'Design not found',
        });
        return;
      }

      const updatedDesign = await db('designs')
        .where({ id, user_id: userId })
        .update({
          name: name || design.name,
          description: description || design.description,
          tags: tags ? JSON.stringify(tags) : design.tags,
          status: status || design.status,
          updated_at: new Date(),
        })
        .returning('*');

      res.json({
        success: true,
        data: updatedDesign[0],
        message: 'Design updated successfully',
      });
    } catch (error) {
      console.error('Error updating design:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update design',
      });
    }
  }

  /**
   * Delete design
   */
  async deleteDesign(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const design = await db('designs')
        .where({ id, user_id: userId })
        .first();

      if (!design) {
        res.status(404).json({
          success: false,
          error: 'Design not found',
        });
        return;
      }

      // Delete files from storage
      try {
        await this.storageService.deleteFileByUrl(design.file_url);
        if (design.thumbnail_url) {
          await this.storageService.deleteFileByUrl(design.thumbnail_url);
        }
        if (design.ai_video_url) {
          await this.storageService.deleteFileByUrl(design.ai_video_url);
        }
        const aiMockupUrls = JSON.parse(design.ai_mockup_urls || '[]');
        for (const url of aiMockupUrls) {
          await this.storageService.deleteFileByUrl(url);
        }
      } catch (storageError) {
        console.error('Error deleting files from storage:', storageError);
      }

      // Delete design record
      await db('designs').where({ id, user_id: userId }).del();

      res.json({
        success: true,
        message: 'Design deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting design:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete design',
      });
    }
  }

  /**
   * Generate AI video for design
   */
  async generateAIVideo(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { prompt, duration, style } = req.body;
      const userId = req.user?.id;

      const design = await db('designs')
        .where({ id, user_id: userId })
        .first();

      if (!design) {
        res.status(404).json({
          success: false,
          error: 'Design not found',
        });
        return;
      }

      // Update status to processing
      await db('designs')
        .where({ id })
        .update({ status: DesignStatus.PROCESSING });

      // Generate video using AI service
      const videoResponse = await this.aiService.generateVideo({
        design_id: id,
        prompt,
        duration,
        style,
      });

      // Update design with video URL
      await db('designs')
        .where({ id })
        .update({
          ai_video_url: videoResponse.video_url,
          status: DesignStatus.READY,
          updated_at: new Date(),
        });

      res.json({
        success: true,
        data: {
          video_url: videoResponse.video_url,
          thumbnail_url: videoResponse.thumbnail_url,
          duration: videoResponse.duration,
        },
        message: 'AI video generated successfully',
      });
    } catch (error) {
      console.error('Error generating AI video:', error);
      
      // Reset status on error
      await db('designs')
        .where({ id: req.params.id })
        .update({ status: DesignStatus.DRAFT });

      res.status(500).json({
        success: false,
        error: 'Failed to generate AI video',
      });
    }
  }

  /**
   * Generate AI mockups for design
   */
  async generateAIMockups(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { product_type, style, background } = req.body;
      const userId = req.user?.id;

      const design = await db('designs')
        .where({ id, user_id: userId })
        .first();

      if (!design) {
        res.status(404).json({
          success: false,
          error: 'Design not found',
        });
        return;
      }

      // Generate mockups using AI service
      const mockupResponse = await this.aiService.generateMockups({
        design_id: id,
        product_type,
        style,
        background,
      });

      // Store mockups in R2
      const storedUrls = await this.storageService.uploadMockupsFromUrls(
        mockupResponse.mockup_urls,
        id
      );

      // Update design with mockup URLs
      await db('designs')
        .where({ id })
        .update({
          ai_mockup_urls: JSON.stringify(storedUrls),
          updated_at: new Date(),
        });

      res.json({
        success: true,
        data: {
          mockup_urls: storedUrls,
        },
        message: 'AI mockups generated successfully',
      });
    } catch (error) {
      console.error('Error generating AI mockups:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate AI mockups',
      });
    }
  }
}
