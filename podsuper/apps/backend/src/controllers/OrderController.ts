import { Request, Response } from 'express';
import { ShopifyService } from '@/services/ShopifyService';
import { GearmentService } from '@/services/GearmentService';
import { StorageService } from '@/services/StorageService';
import { Order, OrderStatus, PaymentStatus, FulfillmentStatus } from '@/types';
import knex from 'knex';
import { v4 as uuidv4 } from 'uuid';

const db = knex(require('@/config/database').default.development);

export class OrderController {
  private shopifyService: ShopifyService;
  private gearmentService: GearmentService;
  private storageService: StorageService;

  constructor() {
    this.shopifyService = new ShopifyService();
    this.gearmentService = new GearmentService();
    this.storageService = new StorageService();
  }

  /**
   * Get all orders with filtering and pagination
   */
  async getOrders(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const { 
        page = 1, 
        limit = 20, 
        status, 
        payment_status, 
        fulfillment_status,
        shop_id,
        start_date,
        end_date 
      } = req.query;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'User not authenticated',
        });
        return;
      }

      // Build query with joins to get shop information
      let query = db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId)
        .select(
          'orders.*',
          'shopify_stores.name as shop_name',
          'shopify_stores.shop_domain'
        );

      // Apply filters
      if (status) {
        query = query.where('orders.order_status', status as string);
      }
      if (payment_status) {
        query = query.where('orders.payment_status', payment_status as string);
      }
      if (fulfillment_status) {
        query = query.where('orders.fulfillment_status', fulfillment_status as string);
      }
      if (shop_id) {
        query = query.where('orders.shopify_store_id', shop_id as string);
      }
      if (start_date) {
        query = query.where('orders.created_at', '>=', start_date as string);
      }
      if (end_date) {
        query = query.where('orders.created_at', '<=', end_date as string);
      }

      const offset = (Number(page) - 1) * Number(limit);
      const orders = await query
        .orderBy('orders.created_at', 'desc')
        .limit(Number(limit))
        .offset(offset);

      // Get total count
      const totalQuery = db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId);

      if (status) totalQuery.where('orders.order_status', status as string);
      if (payment_status) totalQuery.where('orders.payment_status', payment_status as string);
      if (fulfillment_status) totalQuery.where('orders.fulfillment_status', fulfillment_status as string);
      if (shop_id) totalQuery.where('orders.shopify_store_id', shop_id as string);
      if (start_date) totalQuery.where('orders.created_at', '>=', start_date as string);
      if (end_date) totalQuery.where('orders.created_at', '<=', end_date as string);

      const total = await totalQuery.count('* as count').first();

      res.json({
        success: true,
        data: orders,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: Number(total?.count || 0),
          pages: Math.ceil(Number(total?.count || 0) / Number(limit)),
        },
      });
    } catch (error) {
      console.error('Error fetching orders:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch orders',
      });
    }
  }

  /**
   * Get single order with details
   */
  async getOrder(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      const order = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('orders.id', id)
        .where('shopify_stores.user_id', userId)
        .select(
          'orders.*',
          'shopify_stores.name as shop_name',
          'shopify_stores.shop_domain'
        )
        .first();

      if (!order) {
        res.status(404).json({
          success: false,
          error: 'Order not found',
        });
        return;
      }

      // Get Gearment orders for this order
      const gearmentOrders = await db('gearment_orders')
        .where('order_id', id);

      res.json({
        success: true,
        data: {
          ...order,
          gearment_orders: gearmentOrders,
        },
      });
    } catch (error) {
      console.error('Error fetching order:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch order',
      });
    }
  }

  /**
   * Approve order for processing
   */
  async approveOrder(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { notes } = req.body;
      const userId = req.user?.id;

      const order = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('orders.id', id)
        .where('shopify_stores.user_id', userId)
        .select('orders.*')
        .first();

      if (!order) {
        res.status(404).json({
          success: false,
          error: 'Order not found',
        });
        return;
      }

      if (order.payment_status !== 'paid') {
        res.status(400).json({
          success: false,
          error: 'Order must be paid before approval',
        });
        return;
      }

      // Update order status
      await db('orders')
        .where('id', id)
        .update({
          order_status: OrderStatus.APPROVED,
          approved_at: new Date(),
          notes: notes || order.notes,
          updated_at: new Date(),
        });

      // Trigger fulfillment process
      await this.createGearmentOrders(id);

      res.json({
        success: true,
        message: 'Order approved and sent for fulfillment',
      });
    } catch (error) {
      console.error('Error approving order:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to approve order',
      });
    }
  }

  /**
   * Create Gearment orders for approved order
   */
  private async createGearmentOrders(orderId: string): Promise<void> {
    try {
      const order = await db('orders').where('id', orderId).first();
      if (!order) return;

      const lineItems = JSON.parse(order.line_items);
      const shippingAddress = JSON.parse(order.shipping_address);

      for (const lineItem of lineItems) {
        // Get design file URL from R2
        const design = await db('designs').where('id', lineItem.design_id).first();
        if (!design) continue;

        // Create Gearment order for this line item
        const gearmentOrderData = {
          external_order_id: order.shopify_order_id,
          customer: {
            email: order.customer_email,
            first_name: shippingAddress.first_name,
            last_name: shippingAddress.last_name,
            phone: shippingAddress.phone,
          },
          shipping_address: shippingAddress,
          line_items: [{
            external_line_item_id: lineItem.id,
            product_id: lineItem.product_id,
            variant_id: lineItem.variant_id,
            quantity: lineItem.quantity,
            design_url: design.file_url,
            print_areas: [{
              position: 'front',
              design_url: design.file_url,
              x: 0.5,
              y: 0.5,
              scale: 1,
              angle: 0,
            }],
          }],
          notes: order.notes,
        };

        const gearmentResponse = await this.gearmentService.createFulfillmentOrder(gearmentOrderData);

        // Save Gearment order record
        await db('gearment_orders').insert({
          id: uuidv4(),
          order_id: orderId,
          line_item_id: lineItem.id,
          gearment_order_id: gearmentResponse.gearment_order_id,
          status: 'pending',
        });

        // Update line item with Gearment order ID
        lineItem.gearment_order_id = gearmentResponse.gearment_order_id;
      }

      // Update order with modified line items
      await db('orders')
        .where('id', orderId)
        .update({
          line_items: JSON.stringify(lineItems),
          order_status: OrderStatus.PROCESSING,
          updated_at: new Date(),
        });

    } catch (error) {
      console.error('Error creating Gearment orders:', error);
      throw error;
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const userId = req.user?.id;

      const order = await db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('orders.id', id)
        .where('shopify_stores.user_id', userId)
        .select('orders.*')
        .first();

      if (!order) {
        res.status(404).json({
          success: false,
          error: 'Order not found',
        });
        return;
      }

      // Cancel Gearment orders if they exist
      const gearmentOrders = await db('gearment_orders').where('order_id', id);
      for (const gearmentOrder of gearmentOrders) {
        try {
          await this.gearmentService.cancelOrder(gearmentOrder.gearment_order_id, reason);
          await db('gearment_orders')
            .where('id', gearmentOrder.id)
            .update({ status: 'cancelled' });
        } catch (error) {
          console.error('Error cancelling Gearment order:', error);
        }
      }

      // Update order status
      await db('orders')
        .where('id', id)
        .update({
          order_status: OrderStatus.CANCELLED,
          notes: reason ? `${order.notes || ''}\nCancellation reason: ${reason}` : order.notes,
          updated_at: new Date(),
        });

      res.json({
        success: true,
        message: 'Order cancelled successfully',
      });
    } catch (error) {
      console.error('Error cancelling order:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to cancel order',
      });
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStats(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const { start_date, end_date, shop_id } = req.query;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'User not authenticated',
        });
        return;
      }

      let baseQuery = db('orders')
        .join('shopify_stores', 'orders.shopify_store_id', 'shopify_stores.id')
        .where('shopify_stores.user_id', userId);

      if (start_date) {
        baseQuery = baseQuery.where('orders.created_at', '>=', start_date as string);
      }
      if (end_date) {
        baseQuery = baseQuery.where('orders.created_at', '<=', end_date as string);
      }
      if (shop_id) {
        baseQuery = baseQuery.where('orders.shopify_store_id', shop_id as string);
      }

      const [
        totalOrders,
        totalRevenue,
        statusStats,
        paymentStats,
      ] = await Promise.all([
        baseQuery.clone().count('* as count').first(),
        baseQuery.clone().sum('total_price as total').first(),
        baseQuery.clone().select('order_status').count('* as count').groupBy('order_status'),
        baseQuery.clone().select('payment_status').count('* as count').groupBy('payment_status'),
      ]);

      res.json({
        success: true,
        data: {
          total_orders: Number(totalOrders?.count || 0),
          total_revenue: Number(totalRevenue?.total || 0),
          status_breakdown: statusStats,
          payment_breakdown: paymentStats,
        },
      });
    } catch (error) {
      console.error('Error fetching order stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch order statistics',
      });
    }
  }
}
