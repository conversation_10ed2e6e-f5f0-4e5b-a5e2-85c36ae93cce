import { Request, Response } from 'express';
import { ShopifyService } from '@/services/ShopifyService';
import { GearmentService } from '@/services/GearmentService';
import { OrderStatus, PaymentStatus, FulfillmentStatus } from '@/types';
import knex from 'knex';
import { v4 as uuidv4 } from 'uuid';

const db = knex(require('@/config/database').default.development);

export class WebhookController {
  private shopifyService: ShopifyService;
  private gearmentService: GearmentService;

  constructor() {
    this.shopifyService = new ShopifyService();
    this.gearmentService = new GearmentService();
  }

  /**
   * Handle Shopify order created webhook
   */
  async handleShopifyOrderCreated(req: Request, res: Response): Promise<void> {
    try {
      const signature = req.get('X-Shopify-Hmac-Sha256');
      const body = JSON.stringify(req.body);

      if (!signature || !this.shopifyService.verifyWebhook(body, signature)) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const orderData = req.body;
      const shopDomain = req.get('X-Shopify-Shop-Domain');

      // Find the store
      const store = await db('shopify_stores')
        .where('shop_domain', shopDomain)
        .first();

      if (!store) {
        console.error('Store not found for domain:', shopDomain);
        res.status(404).json({ error: 'Store not found' });
        return;
      }

      // Check if order already exists
      const existingOrder = await db('orders')
        .where('shopify_order_id', orderData.id.toString())
        .where('shopify_store_id', store.id)
        .first();

      if (existingOrder) {
        res.status(200).json({ message: 'Order already exists' });
        return;
      }

      // Create new order
      const orderId = uuidv4();
      await db('orders').insert({
        id: orderId,
        shopify_order_id: orderData.id.toString(),
        shopify_store_id: store.id,
        order_number: orderData.order_number || orderData.name,
        customer_email: orderData.customer?.email || '',
        customer_name: `${orderData.customer?.first_name || ''} ${orderData.customer?.last_name || ''}`.trim(),
        shipping_address: JSON.stringify(orderData.shipping_address || {}),
        billing_address: JSON.stringify(orderData.billing_address || orderData.shipping_address || {}),
        line_items: JSON.stringify(orderData.line_items || []),
        total_price: parseFloat(orderData.total_price || '0'),
        currency: orderData.currency || 'USD',
        payment_status: this.mapShopifyPaymentStatus(orderData.financial_status),
        fulfillment_status: this.mapShopifyFulfillmentStatus(orderData.fulfillment_status),
        order_status: OrderStatus.PENDING,
        notes: orderData.note || '',
        tags: JSON.stringify(orderData.tags ? orderData.tags.split(',') : []),
        shopify_data: JSON.stringify(orderData),
        shopify_created_at: new Date(orderData.created_at),
      });

      console.log(`New order created: ${orderData.order_number} for store: ${store.name}`);
      res.status(200).json({ message: 'Order created successfully' });
    } catch (error) {
      console.error('Error handling Shopify order created webhook:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Handle Shopify order updated webhook
   */
  async handleShopifyOrderUpdated(req: Request, res: Response): Promise<void> {
    try {
      const signature = req.get('X-Shopify-Hmac-Sha256');
      const body = JSON.stringify(req.body);

      if (!signature || !this.shopifyService.verifyWebhook(body, signature)) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const orderData = req.body;
      const shopDomain = req.get('X-Shopify-Shop-Domain');

      const store = await db('shopify_stores')
        .where('shop_domain', shopDomain)
        .first();

      if (!store) {
        res.status(404).json({ error: 'Store not found' });
        return;
      }

      // Update existing order
      await db('orders')
        .where('shopify_order_id', orderData.id.toString())
        .where('shopify_store_id', store.id)
        .update({
          payment_status: this.mapShopifyPaymentStatus(orderData.financial_status),
          fulfillment_status: this.mapShopifyFulfillmentStatus(orderData.fulfillment_status),
          notes: orderData.note || '',
          tags: JSON.stringify(orderData.tags ? orderData.tags.split(',') : []),
          shopify_data: JSON.stringify(orderData),
          updated_at: new Date(),
        });

      res.status(200).json({ message: 'Order updated successfully' });
    } catch (error) {
      console.error('Error handling Shopify order updated webhook:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Handle Shopify order paid webhook
   */
  async handleShopifyOrderPaid(req: Request, res: Response): Promise<void> {
    try {
      const signature = req.get('X-Shopify-Hmac-Sha256');
      const body = JSON.stringify(req.body);

      if (!signature || !this.shopifyService.verifyWebhook(body, signature)) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const orderData = req.body;
      const shopDomain = req.get('X-Shopify-Shop-Domain');

      const store = await db('shopify_stores')
        .where('shop_domain', shopDomain)
        .first();

      if (!store) {
        res.status(404).json({ error: 'Store not found' });
        return;
      }

      // Update order payment status
      await db('orders')
        .where('shopify_order_id', orderData.id.toString())
        .where('shopify_store_id', store.id)
        .update({
          payment_status: PaymentStatus.PAID,
          updated_at: new Date(),
        });

      console.log(`Order paid: ${orderData.order_number} for store: ${store.name}`);
      res.status(200).json({ message: 'Order payment status updated' });
    } catch (error) {
      console.error('Error handling Shopify order paid webhook:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Handle Shopify order cancelled webhook
   */
  async handleShopifyOrderCancelled(req: Request, res: Response): Promise<void> {
    try {
      const signature = req.get('X-Shopify-Hmac-Sha256');
      const body = JSON.stringify(req.body);

      if (!signature || !this.shopifyService.verifyWebhook(body, signature)) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const orderData = req.body;
      const shopDomain = req.get('X-Shopify-Shop-Domain');

      const store = await db('shopify_stores')
        .where('shop_domain', shopDomain)
        .first();

      if (!store) {
        res.status(404).json({ error: 'Store not found' });
        return;
      }

      // Update order status
      await db('orders')
        .where('shopify_order_id', orderData.id.toString())
        .where('shopify_store_id', store.id)
        .update({
          order_status: OrderStatus.CANCELLED,
          notes: `${orderData.note || ''}\nCancelled reason: ${orderData.cancel_reason || 'Unknown'}`,
          updated_at: new Date(),
        });

      // Cancel associated Gearment orders
      const order = await db('orders')
        .where('shopify_order_id', orderData.id.toString())
        .where('shopify_store_id', store.id)
        .first();

      if (order) {
        const gearmentOrders = await db('gearment_orders').where('order_id', order.id);
        for (const gearmentOrder of gearmentOrders) {
          try {
            await this.gearmentService.cancelOrder(
              gearmentOrder.gearment_order_id,
              orderData.cancel_reason || 'Shopify order cancelled'
            );
            await db('gearment_orders')
              .where('id', gearmentOrder.id)
              .update({ status: 'cancelled' });
          } catch (error) {
            console.error('Error cancelling Gearment order:', error);
          }
        }
      }

      console.log(`Order cancelled: ${orderData.order_number} for store: ${store.name}`);
      res.status(200).json({ message: 'Order cancelled successfully' });
    } catch (error) {
      console.error('Error handling Shopify order cancelled webhook:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Handle Gearment order status update webhook
   */
  async handleGearmentOrderUpdate(req: Request, res: Response): Promise<void> {
    try {
      const signature = req.get('X-Gearment-Signature');
      const body = JSON.stringify(req.body);

      if (!signature || !this.gearmentService.verifyWebhook(body, signature)) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const gearmentData = req.body;
      const gearmentOrderId = gearmentData.order_id;

      // Find the Gearment order
      const gearmentOrder = await db('gearment_orders')
        .where('gearment_order_id', gearmentOrderId)
        .first();

      if (!gearmentOrder) {
        res.status(404).json({ error: 'Gearment order not found' });
        return;
      }

      // Update Gearment order status
      await db('gearment_orders')
        .where('id', gearmentOrder.id)
        .update({
          status: gearmentData.status,
          tracking_number: gearmentData.tracking_number,
          tracking_url: gearmentData.tracking_url,
          tracking_company: gearmentData.tracking_company,
          shipped_at: gearmentData.shipped_at ? new Date(gearmentData.shipped_at) : null,
          delivered_at: gearmentData.delivered_at ? new Date(gearmentData.delivered_at) : null,
          notes: gearmentData.notes,
          gearment_data: JSON.stringify(gearmentData),
          updated_at: new Date(),
        });

      // Update main order status if all items are shipped/delivered
      if (gearmentData.status === 'shipped' || gearmentData.status === 'delivered') {
        const order = await db('orders').where('id', gearmentOrder.order_id).first();
        if (order) {
          const allGearmentOrders = await db('gearment_orders')
            .where('order_id', gearmentOrder.order_id);

          const allShipped = allGearmentOrders.every(go => 
            ['shipped', 'delivered'].includes(go.status)
          );

          if (allShipped) {
            const newStatus = gearmentData.status === 'delivered' ? 
              OrderStatus.DELIVERED : OrderStatus.SHIPPED;

            await db('orders')
              .where('id', gearmentOrder.order_id)
              .update({
                order_status: newStatus,
                fulfillment_status: FulfillmentStatus.FULFILLED,
                updated_at: new Date(),
              });

            // Update Shopify order with tracking info
            try {
              const store = await db('shopify_stores')
                .where('id', order.shopify_store_id)
                .first();

              if (store && gearmentData.tracking_number) {
                await this.shopifyService.createFulfillment(store, order.shopify_order_id, {
                  line_items: JSON.parse(order.line_items).map((item: any) => ({
                    id: item.id,
                    quantity: item.quantity,
                  })),
                  tracking_number: gearmentData.tracking_number,
                  tracking_company: gearmentData.tracking_company,
                  tracking_url: gearmentData.tracking_url,
                  notify_customer: true,
                });
              }
            } catch (error) {
              console.error('Error updating Shopify fulfillment:', error);
            }
          }
        }
      }

      console.log(`Gearment order updated: ${gearmentOrderId} - Status: ${gearmentData.status}`);
      res.status(200).json({ message: 'Gearment order updated successfully' });
    } catch (error) {
      console.error('Error handling Gearment order update webhook:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Map Shopify payment status to internal status
   */
  private mapShopifyPaymentStatus(shopifyStatus: string): PaymentStatus {
    const statusMap: { [key: string]: PaymentStatus } = {
      'pending': PaymentStatus.PENDING,
      'authorized': PaymentStatus.AUTHORIZED,
      'partially_paid': PaymentStatus.PARTIALLY_PAID,
      'paid': PaymentStatus.PAID,
      'partially_refunded': PaymentStatus.PARTIALLY_REFUNDED,
      'refunded': PaymentStatus.REFUNDED,
      'voided': PaymentStatus.VOIDED,
    };
    return statusMap[shopifyStatus] || PaymentStatus.PENDING;
  }

  /**
   * Map Shopify fulfillment status to internal status
   */
  private mapShopifyFulfillmentStatus(shopifyStatus: string): FulfillmentStatus {
    const statusMap: { [key: string]: FulfillmentStatus } = {
      'fulfilled': FulfillmentStatus.FULFILLED,
      'partial': FulfillmentStatus.PARTIAL,
      'unfulfilled': FulfillmentStatus.UNFULFILLED,
    };
    return statusMap[shopifyStatus] || FulfillmentStatus.UNFULFILLED;
  }
}
