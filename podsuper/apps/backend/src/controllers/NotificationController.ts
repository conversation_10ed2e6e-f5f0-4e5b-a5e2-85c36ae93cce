import { Request, Response } from 'express';
import { db } from '@/config/database';
import { logger } from '@/middleware/logger';

export class NotificationController {
  // Get user notifications
  async getNotifications(req: Request, res: Response) {
    try {
      const userId = req.user?.id;
      const { type, read, limit = 50 } = req.query;

      let query = db('notifications')
        .where('user_id', userId)
        .orWhere('user_id', null) // Global notifications
        .orderBy('created_at', 'desc')
        .limit(Number(limit));

      if (type) {
        query = query.where('type', type);
      }

      if (read !== undefined) {
        query = query.where('read', read === 'true');
      }

      const notifications = await query;

      // Generate mock notifications for demo
      const mockNotifications = [
        {
          id: '1',
          type: 'order',
          title: 'New Order Received',
          message: 'Order #12345 from John <PERSON> - Summer T-Shirt Design',
          read: false,
          priority: 'high',
          created_at: new Date(Date.now() - 300000).toISOString(),
          data: { order_id: '12345', amount: 29.99 },
          action_url: '/orders/12345'
        },
        {
          id: '2',
          type: 'design',
          title: 'Design Approved',
          message: 'Your design "Vintage Logo Collection" has been approved and is now live',
          read: false,
          priority: 'medium',
          created_at: new Date(Date.now() - 900000).toISOString(),
          data: { design_id: 'vintage-logo' },
          action_url: '/designs/vintage-logo'
        },
        {
          id: '3',
          type: 'payment',
          title: 'Payment Received',
          message: 'Payment of $450.75 has been processed successfully',
          read: true,
          priority: 'medium',
          created_at: new Date(Date.now() - 3600000).toISOString(),
          data: { amount: 450.75, transaction_id: 'txn_123' }
        },
        {
          id: '4',
          type: 'system',
          title: 'System Maintenance',
          message: 'Scheduled maintenance will occur tonight from 2-4 AM EST',
          read: false,
          priority: 'urgent',
          created_at: new Date(Date.now() - 7200000).toISOString(),
        },
        {
          id: '5',
          type: 'user',
          title: 'New User Registration',
          message: 'Jane Smith has registered and is awaiting approval',
          read: true,
          priority: 'low',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          data: { user_id: 'jane-smith' },
          action_url: '/admin/users'
        },
        {
          id: '6',
          type: 'alert',
          title: 'Low Stock Alert',
          message: 'T-Shirt inventory is running low (5 items remaining)',
          read: false,
          priority: 'high',
          created_at: new Date(Date.now() - 1800000).toISOString(),
          data: { product_type: 't-shirt', stock: 5 }
        }
      ];

      res.json({
        success: true,
        data: notifications.length > 0 ? notifications : mockNotifications
      });
    } catch (error) {
      logger.error('Get notifications error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch notifications'
      });
    }
  }

  // Mark notification as read
  async markAsRead(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      await db('notifications')
        .where('id', id)
        .where('user_id', userId)
        .update({
          read: true,
          updated_at: new Date()
        });

      res.json({
        success: true,
        message: 'Notification marked as read'
      });
    } catch (error) {
      logger.error('Mark notification as read error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to mark notification as read'
      });
    }
  }

  // Mark notification as unread
  async markAsUnread(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      await db('notifications')
        .where('id', id)
        .where('user_id', userId)
        .update({
          read: false,
          updated_at: new Date()
        });

      res.json({
        success: true,
        message: 'Notification marked as unread'
      });
    } catch (error) {
      logger.error('Mark notification as unread error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to mark notification as unread'
      });
    }
  }

  // Mark all notifications as read
  async markAllAsRead(req: Request, res: Response) {
    try {
      const userId = req.user?.id;

      await db('notifications')
        .where('user_id', userId)
        .orWhere('user_id', null)
        .update({
          read: true,
          updated_at: new Date()
        });

      res.json({
        success: true,
        message: 'All notifications marked as read'
      });
    } catch (error) {
      logger.error('Mark all notifications as read error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to mark all notifications as read'
      });
    }
  }

  // Delete notification
  async deleteNotification(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      await db('notifications')
        .where('id', id)
        .where('user_id', userId)
        .del();

      res.json({
        success: true,
        message: 'Notification deleted'
      });
    } catch (error) {
      logger.error('Delete notification error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete notification'
      });
    }
  }

  // Clear all notifications
  async clearAll(req: Request, res: Response) {
    try {
      const userId = req.user?.id;

      await db('notifications')
        .where('user_id', userId)
        .del();

      res.json({
        success: true,
        message: 'All notifications cleared'
      });
    } catch (error) {
      logger.error('Clear all notifications error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to clear notifications'
      });
    }
  }

  // Create notification (admin only)
  async createNotification(req: Request, res: Response) {
    try {
      const { type, title, message, priority, user_id, data, action_url } = req.body;

      const [notification] = await db('notifications')
        .insert({
          id: db.raw('gen_random_uuid()'),
          type,
          title,
          message,
          priority: priority || 'medium',
          user_id: user_id || null, // null for global notifications
          data: data ? JSON.stringify(data) : null,
          action_url,
          read: false,
          created_at: new Date(),
          updated_at: new Date()
        })
        .returning('*');

      // Broadcast via WebSocket if available
      if (global.wsService) {
        if (user_id) {
          global.wsService.sendNotificationToUser(user_id, {
            type: 'notification',
            title,
            message,
            data: notification
          });
        } else {
          // Global notification
          global.wsService.sendNotificationToAdmins({
            type: 'notification',
            title,
            message,
            data: notification
          });
        }
      }

      logger.info(`Notification created: ${title}`);

      res.status(201).json({
        success: true,
        data: notification,
        message: 'Notification created successfully'
      });
    } catch (error) {
      logger.error('Create notification error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create notification'
      });
    }
  }
}
