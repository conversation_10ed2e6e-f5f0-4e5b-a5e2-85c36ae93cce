{"name": "backend", "version": "1.0.0", "description": "Backend API for Design Fulfillment System", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/index.ts", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "migrate:status": "knex migrate:status", "seed": "knex seed:run", "db:setup": "./scripts/dev-setup.sh", "db:reset": "knex migrate:rollback --all && knex migrate:latest"}, "dependencies": {"@aws-sdk/client-s3": "^3.400.0", "@aws-sdk/s3-request-presigner": "^3.400.0", "@repo/config": "workspace:*", "@repo/types": "workspace:*", "axios": "^1.5.0", "bcryptjs": "^2.4.3", "bull": "^4.11.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "knex": "^2.5.1", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "redis": "^4.6.8", "sharp": "^0.32.5", "socket.io": "^4.8.1", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.4", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.11", "@types/node": "^20.5.0", "@types/socket.io": "^3.0.2", "@types/uuid": "^9.0.2", "jest": "^29.6.2", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6"}}