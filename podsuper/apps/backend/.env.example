# Environment
NODE_ENV=development
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=design_fulfillment
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# File Upload
MAX_FILE_SIZE=********

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your-r2-account-id
R2_ACCESS_KEY_ID=your-r2-access-key
R2_SECRET_ACCESS_KEY=your-r2-secret-key
R2_BUCKET_NAME=design-assets
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
R2_PUBLIC_URL=https://your-domain.com

# AI Services
KLING_AI_API_KEY=your-kling-ai-api-key
KLING_AI_BASE_URL=https://api.kling.ai
MOCKUP_AI_API_KEY=your-mockup-ai-api-key
MOCKUP_AI_BASE_URL=https://api.mockup.ai

# Printify Configuration
PRINTIFY_API_KEY=your-printify-api-key
PRINTIFY_BASE_URL=https://api.printify.com/v1
PRINTIFY_SHOP_ID=your-printify-shop-id

# Shopify Configuration
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
SHOPIFY_WEBHOOK_SECRET=your-shopify-webhook-secret
SHOPIFY_SCOPES=read_products,write_products,read_orders,write_orders

# Gearment Configuration
GEARMENT_API_KEY=your-gearment-api-key
GEARMENT_BASE_URL=https://api.gearment.com
GEARMENT_WEBHOOK_SECRET=your-gearment-webhook-secret

# Email Configuration (Optional)
EMAIL_FROM=<EMAIL>
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=

# Frontend URLs
FRONTEND_URL=http://localhost:3001
DASHBOARD_URL=http://localhost:3001/dashboard

# API URLs
API_BASE_URL=http://localhost:3000/api
WEBHOOK_URL=http://localhost:3000/api/webhooks

# WebSocket Configuration
WS_PORT=3001
WS_CORS_ORIGIN=http://localhost:3001
