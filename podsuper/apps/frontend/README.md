# Design Fulfillment System - Frontend

Modern React/Next.js frontend for the Design to Fulfillment automation platform.

## 🚀 Features

### ✅ Phase 1: Authentication & Layout (Completed)
- **Authentication System**: Login, register, password reset with JWT
- **Responsive Layout**: Collapsible sidebar, mobile-friendly design
- **State Management**: Zustand stores for auth and UI state
- **UI Components**: Shadcn/ui components with Tailwind CSS
- **Type Safety**: Comprehensive TypeScript types
- **API Integration**: Axios client with auto token refresh

## 🛠️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui + Radix UI
- **State Management**: Zustand
- **API Client**: Axios + React Query
- **Forms**: React Hook Form + Zod validation
- **Icons**: Lucide React

## 📦 Installation

1. **Install dependencies**:
   ```bash
   cd frontend
   npm install
   ```

2. **Environment setup**:
   ```bash
   cp .env.example .env
   # Update .env with your API URL
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Open browser**: http://localhost:3000

## 🏗️ Project Structure

```
frontend/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   │   ├── login/         # Login page
│   │   └── register/      # Register page
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── ui/               # Shadcn/ui components
│   ├── layout/           # Layout components
│   └── providers.tsx     # App providers
├── lib/                  # Utilities
│   ├── api.ts           # API client
│   ├── auth.ts          # Auth service
│   └── utils.ts         # Helper functions
├── stores/              # Zustand stores
│   ├── auth-store.ts    # Authentication state
│   └── ui-store.ts      # UI state
├── types/               # TypeScript types
│   └── index.ts         # Type definitions
└── middleware.ts        # Route protection
```

## 🔐 Authentication Flow

1. **Login/Register**: Form validation with Zod schemas
2. **JWT Storage**: Secure token management with auto-refresh
3. **Route Protection**: Middleware-based route guards
4. **State Persistence**: Zustand with localStorage persistence

## 🎨 UI Components

### Available Components
- **Button**: Multiple variants and sizes
- **Input**: Form inputs with validation states
- **Card**: Content containers
- **Badge**: Status indicators
- **Toast**: Notification system
- **Label**: Form labels

### Layout Components
- **Sidebar**: Collapsible navigation with mobile support
- **Header**: Top navigation bar
- **Main Layout**: Responsive grid layout

## 📱 Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Navigation**: Collapsible sidebar → mobile overlay
- **Touch Friendly**: Optimized touch targets

## 🔧 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

### Code Quality
- **TypeScript**: Strict type checking
- **ESLint**: Code linting with Next.js config
- **Prettier**: Code formatting (recommended)

## 🌐 API Integration

### API Client Features
- **Auto Token Refresh**: Seamless token renewal
- **Request Interceptors**: Automatic auth headers
- **Error Handling**: Comprehensive error responses
- **File Upload**: Progress tracking support

### Usage Example
```typescript
import apiClient from '@/lib/api'

// GET request
const response = await apiClient.get('/designs')

// POST with file upload
const result = await apiClient.uploadFile('/designs', file, (progress) => {
  console.log(`Upload progress: ${progress}%`)
})
```

## 🎯 State Management

### Auth Store
```typescript
const { user, login, logout, isAuthenticated } = useAuthStore()
```

### UI Store
```typescript
const { notifications, addNotification } = useNotifications()
const { isOpen, toggle } = useSidebar()
```

## 🚧 Upcoming Features

### Phase 2: Dashboard & Analytics
- Overview dashboard with metrics
- Real-time charts and analytics
- WebSocket integration for live updates

### Phase 3: Design Management
- File upload with drag & drop
- Design library with search/filter
- AI content generation interface

### Phase 4: Order Management
- Order dashboard with advanced filtering
- Bulk operations and status updates
- Real-time order tracking

## 🤝 Contributing

1. Follow TypeScript best practices
2. Use Shadcn/ui components when possible
3. Implement proper error handling
4. Add loading states for async operations
5. Ensure mobile responsiveness

## 📄 License

This project is part of the Design Fulfillment System.
