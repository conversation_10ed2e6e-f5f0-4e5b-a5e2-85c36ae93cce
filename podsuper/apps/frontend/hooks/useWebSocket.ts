'use client'

import { useEffect, useRef, useState } from 'react'
import { io, Socket } from 'socket.io-client'
import { useAuthStore } from '@/stores/auth-store'
import { useUIStore } from '@/stores/ui-store'

interface WebSocketMessage {
  type: string
  data?: any
  message?: string
  title?: string
  timestamp: string
}

interface UseWebSocketOptions {
  autoConnect?: boolean
  subscriptions?: string[]
}

export function useWebSocket(options: UseWebSocketOptions = {}): {
  isConnected: boolean
  connectionError: string | null
  connect: () => void
  disconnect: () => void
  subscribe: (channel: string) => void
  unsubscribe: (channel: string) => void
  emit: (event: string, data?: any) => void
  socket: Socket | null
} {
  const { autoConnect = true, subscriptions = [] } = options
  const { token } = useAuthStore()
  const { addNotification } = useUIStore()
  
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const socketRef = useRef<Socket | null>(null)

  const connect = () => {
    if (!token) {
      setConnectionError('No authentication token available')
      return
    }

    if (socketRef.current?.connected) {
      return // Already connected
    }

    const socket = io(process.env.NEXT_PUBLIC_API_URL?.replace('/api', '') || 'http://localhost:3000', {
      auth: {
        token
      },
      transports: ['websocket', 'polling']
    })

    socketRef.current = socket

    // Connection events
    socket.on('connect', () => {
      console.log('✅ WebSocket connected')
      setIsConnected(true)
      setConnectionError(null)
      
      // Subscribe to requested channels
      subscriptions.forEach(subscription => {
        socket.emit(`subscribe:${subscription}`)
      })
    })

    socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason)
      setIsConnected(false)
    })

    socket.on('connect_error', (error) => {
      console.error('🔌 WebSocket connection error:', error)
      setConnectionError(error.message)
      setIsConnected(false)
    })

    // Welcome message
    socket.on('connected', (data) => {
      console.log('🎉 WebSocket welcome:', data)
    })

    // Dashboard updates
    socket.on('dashboard:stats', (message: WebSocketMessage) => {
      console.log('📊 Dashboard stats update:', message)
      // Trigger dashboard refresh
      window.dispatchEvent(new CustomEvent('dashboard:refresh', { detail: message.data }))
    })

    // Order notifications
    socket.on('order:new', (message: WebSocketMessage) => {
      console.log('🛒 New order:', message)
      addNotification({
        type: 'success',
        title: 'New Order Received',
        message: `Order received at ${new Date(message.timestamp).toLocaleTimeString()}`,
        duration: 5000
      })
      
      // Trigger orders refresh
      window.dispatchEvent(new CustomEvent('orders:refresh', { detail: message.data }))
    })

    socket.on('order:update', (message: WebSocketMessage) => {
      console.log('📦 Order update:', message)
      // Trigger orders refresh
      window.dispatchEvent(new CustomEvent('orders:refresh', { detail: message.data }))
    })

    // Design notifications
    socket.on('design:new', (message: WebSocketMessage) => {
      console.log('🎨 New design:', message)
      addNotification({
        type: 'success',
        title: 'New Design Uploaded',
        message: `Design uploaded at ${new Date(message.timestamp).toLocaleTimeString()}`,
        duration: 5000
      })
      
      // Trigger designs refresh
      window.dispatchEvent(new CustomEvent('designs:refresh', { detail: message.data }))
    })

    socket.on('design:update', (message: WebSocketMessage) => {
      console.log('🖼️ Design update:', message)
      // Trigger designs refresh
      window.dispatchEvent(new CustomEvent('designs:refresh', { detail: message.data }))
    })

    // General notifications
    socket.on('notification', (message: WebSocketMessage) => {
      console.log('🔔 Notification:', message)
      
      let notificationType: 'success' | 'error' | 'warning' | 'info' = 'info'
      
      if (message.type?.includes('error') || message.type?.includes('failed')) {
        notificationType = 'error'
      } else if (message.type?.includes('warning')) {
        notificationType = 'warning'
      } else if (message.type?.includes('success') || message.type?.includes('completed')) {
        notificationType = 'success'
      }
      
      addNotification({
        type: notificationType,
        title: message.title || 'Notification',
        message: message.message || 'You have a new notification',
        duration: 5000
      })
    })

    // System messages
    socket.on('system:message', (message: WebSocketMessage) => {
      console.log('🔧 System message:', message)
      
      let notificationType: 'success' | 'error' | 'warning' | 'info' = 'info'
      if (message.type === 'error') notificationType = 'error'
      else if (message.type === 'warning') notificationType = 'warning'
      
      addNotification({
        type: notificationType,
        title: 'System Message',
        message: message.message || 'System notification',
        duration: 8000
      })
    })
  }

  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect()
      socketRef.current = null
      setIsConnected(false)
    }
  }

  const subscribe = (channel: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(`subscribe:${channel}`)
      console.log(`📡 Subscribed to ${channel}`)
    }
  }

  const unsubscribe = (channel: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(`unsubscribe:${channel}`)
      console.log(`📡 Unsubscribed from ${channel}`)
    }
  }

  const emit = (event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data)
    }
  }

  // Auto-connect when token is available
  useEffect(() => {
    if (autoConnect && token && !socketRef.current?.connected) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [token, autoConnect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [])

  return {
    isConnected,
    connectionError,
    connect,
    disconnect,
    subscribe,
    unsubscribe,
    emit,
    socket: socketRef.current
  }
}
