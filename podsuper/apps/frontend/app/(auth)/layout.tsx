import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Authentication - Design Fulfillment System',
  description: 'Sign in to your Design Fulfillment System account',
}

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">
            Design Fulfillment System
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Complete design to fulfillment automation
          </p>
        </div>
        {children}
      </div>
    </div>
  )
}
