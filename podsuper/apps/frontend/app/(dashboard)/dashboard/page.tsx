'use client'

import { useEffect, useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  TrendingUp, 
  TrendingDown, 
  Package, 
  ShoppingCart, 
  DollarSign, 
  Users,
  Activity,
  Calendar,
  Filter,
  Download
} from 'lucide-react'
import { DashboardStats, RecentActivity } from '@repo/types'
import api from '@/lib/api'
import { useAuthStore } from '@/stores/auth-store'
import { useUIStore } from '@/stores/ui-store'
// import { useWebSocket } from '@/hooks/useWebSocket'
import { RevenueChart } from '@/components/dashboard/revenue-chart'
import { OrdersChart } from '@/components/dashboard/orders-chart'
import { RecentActivityList } from '@/components/dashboard/recent-activity'
import { StatsCards } from '@/components/dashboard/stats-cards'
import { DateRangePicker } from '@/components/dashboard/date-range-picker'
import { AdvancedAnalytics } from '@/components/dashboard/advanced-analytics'

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuthStore()
  const { addNotification } = useUIStore()
  const [isInitialized, setIsInitialized] = useState(false)

  // Wait for auth state to be initialized from localStorage
  useEffect(() => {
    // Give time for zustand persist to hydrate
    const timer = setTimeout(() => {
      console.log('🔄 Dashboard initialization complete')
      setIsInitialized(true)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  // Redirect to login if not authenticated (only after initialization)
  useEffect(() => {
    if (isInitialized && !authLoading && !isAuthenticated && !user) {
      window.location.href = '/login'
      return
    }
  }, [isInitialized, authLoading, isAuthenticated, user])
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    to: new Date()
  })

  // Temporarily disable WebSocket to prevent refresh loop
  // const { isConnected } = useWebSocket({
  //   autoConnect: false,
  //   subscriptions: ['dashboard', 'orders', 'designs']
  // })
  const isConnected = false

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true)

      // Fetch dashboard stats
      const statsResponse = await api.get('/dashboard/stats', {
        params: {
          from: dateRange.from.toISOString(),
          to: dateRange.to.toISOString()
        }
      })

      if ((statsResponse.data as any).success) {
        setStats((statsResponse.data as any).data)
      }

      // Fetch recent activity
      const activityResponse = await api.get('/dashboard/activity', {
        params: { limit: 10 }
      })

      if ((activityResponse.data as any).success) {
        setRecentActivity((activityResponse.data as any).data)
      }

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
      // Use addNotification without including it in dependencies to avoid loops
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load dashboard data'
      })
    } finally {
      setLoading(false)
    }
  }, [dateRange]) // Remove addNotification from dependencies

  useEffect(() => {
    fetchDashboardData()
  }, [fetchDashboardData])

  // Temporarily disable WebSocket event listener to prevent refresh loop
  // useEffect(() => {
  //   let timeoutId: NodeJS.Timeout

  //   const handleDashboardRefresh = (_event: CustomEvent) => {
  //     console.log('🔄 Dashboard refresh triggered by WebSocket')

  //     // Clear previous timeout
  //     if (timeoutId) {
  //       clearTimeout(timeoutId)
  //     }

  //     // Debounce the refresh to avoid too frequent updates
  //     timeoutId = setTimeout(() => {
  //       fetchDashboardData()
  //     }, 1000) // Wait 1 second before refreshing
  //   }

  //   window.addEventListener('dashboard:refresh', handleDashboardRefresh as EventListener)

  //   return () => {
  //     if (timeoutId) {
  //       clearTimeout(timeoutId)
  //     }
  //     window.removeEventListener('dashboard:refresh', handleDashboardRefresh as EventListener)
  //   }
  // }, [fetchDashboardData])

  const handleExportData = async () => {
    try {
      const response = await api.get('/dashboard/export', {
        params: {
          from: dateRange.from.toISOString(),
          to: dateRange.to.toISOString(),
          format: 'csv'
        },
        responseType: 'blob'
      })

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data as any]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `dashboard-data-${Date.now()}.csv`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      
      addNotification({
        type: 'success',
        title: 'Export Complete',
        message: 'Dashboard data exported successfully'
      })
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Export Failed',
        message: 'Failed to export dashboard data'
      })
    }
  }

  // Show loading while auth is initializing
  if (!isInitialized || authLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render anything if not authenticated (will redirect)
  if (!isAuthenticated || !user) {
    return null
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
              isConnected
                ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
                : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
            }`}>
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span>{isConnected ? 'Live' : 'Offline'}</span>
            </div>
          </div>
          <p className="text-muted-foreground">
            Welcome back, {user?.first_name}! Here's what's happening with your business.
          </p>
        </div>
        
        <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
          />
          <Button variant="outline" onClick={handleExportData}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <StatsCards stats={stats} loading={loading} />

      {/* Charts */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Revenue Overview</CardTitle>
            <CardDescription>
              Your revenue performance over the selected period
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <RevenueChart dateRange={dateRange} />
          </CardContent>
        </Card>
        
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest updates from your business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentActivityList activities={recentActivity} />
          </CardContent>
        </Card>
      </div>

      {/* Orders Chart */}
      <div className="grid gap-4 md:grid-cols-1">
        <Card>
          <CardHeader>
            <CardTitle>Orders Analytics</CardTitle>
            <CardDescription>
              Track your order volume and trends
            </CardDescription>
          </CardHeader>
          <CardContent>
            <OrdersChart dateRange={dateRange} />
          </CardContent>
        </Card>
      </div>

      {/* Advanced Analytics */}
      {user?.role === 'admin' && (
        <div className="mt-8">
          <AdvancedAnalytics />
        </div>
      )}
    </div>
  )
}
