'use client'

import { Sidebar } from '@/components/layout/sidebar'
import { Header } from '@/components/layout/header'
import { Providers } from '@/components/providers'
import { useSidebar } from '@/stores/ui-store'
import { cn } from '@/lib/utils'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <Providers>
      <DashboardLayoutContent>{children}</DashboardLayoutContent>
    </Providers>
  )
}

function DashboardLayoutContent({ children }: { children: React.ReactNode }) {
  const { isCollapsed, isOpen } = useSidebar()

  return (
    <div className="flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
      <Sidebar />
      <div
        className={cn(
          "flex flex-col flex-1 overflow-hidden transition-all duration-300",
          // Add margin-left to prevent content from being hidden behind sidebar on desktop
          "lg:ml-64", // Default sidebar width
          isCollapsed && "lg:ml-16", // Collapsed sidebar width
          // On mobile, no margin when sidebar is closed
          "ml-0"
        )}
      >
        <Header />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900">
          {children}
        </main>
      </div>
    </div>
  )
}
