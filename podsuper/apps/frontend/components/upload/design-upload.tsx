'use client'

import { useState, useCallback, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/ui/select'
import { 
  Upload, X, Image, FileText, Check, AlertCircle, 
  Eye, Download, Trash2, RefreshCw, Plus
} from 'lucide-react'
import { useDropzone } from 'react-dropzone'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import api from '@/lib/api'
import { useUIStore } from '@/stores/ui-store'
import { useWebSocket } from '@/hooks/useWebSocket'

interface UploadFile {
  id: string
  file: File
  preview: string
  progress: number
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
  designId?: string
}

const designFormSchema = z.object({
  name: z.string().min(1, 'Design name is required'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  tags: z.string().optional(),
  price: z.number().min(0, 'Price must be positive'),
  is_public: z.boolean()
})

type DesignFormData = z.infer<typeof designFormSchema>

const ACCEPTED_FILE_TYPES = {
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/gif': ['.gif'],
  'image/webp': ['.webp'],
  'image/svg+xml': ['.svg']
}

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

export function DesignUpload() {
  const [files, setFiles] = useState<UploadFile[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { success, error } = useUIStore()
  const { emit } = useWebSocket()

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors }
  } = useForm<DesignFormData>({
    resolver: zodResolver(designFormSchema),
    defaultValues: {
      is_public: true,
      price: 0
    }
  })

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      preview: URL.createObjectURL(file),
      progress: 0,
      status: 'pending'
    }))

    setFiles(prev => [...prev, ...newFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxSize: MAX_FILE_SIZE,
    multiple: true,
    onDropRejected: (rejectedFiles) => {
      rejectedFiles.forEach(rejection => {
        const { file, errors } = rejection
        if (errors.some(e => e.code === 'file-too-large')) {
          error(`File ${file.name} is too large. Maximum size is 10MB.`)
        } else if (errors.some(e => e.code === 'file-invalid-type')) {
          error(`File ${file.name} has invalid type. Only images are allowed.`)
        }
      })
    }
  })

  const removeFile = (fileId: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === fileId)
      if (file?.preview) {
        URL.revokeObjectURL(file.preview)
      }
      return prev.filter(f => f.id !== fileId)
    })
  }

  const uploadFile = async (uploadFile: UploadFile, formData: DesignFormData) => {
    const data = new FormData()
    data.append('file', uploadFile.file)
    data.append('name', formData.name)
    data.append('description', formData.description || '')
    data.append('category', formData.category)
    data.append('tags', formData.tags || '')
    data.append('price', formData.price.toString())
    data.append('is_public', formData.is_public.toString())

    try {
      // Update status to uploading
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'uploading' as const }
          : f
      ))

      const response = await api.post('/designs/upload', data, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const progress = progressEvent.total 
            ? Math.round((progressEvent.loaded * 100) / progressEvent.total)
            : 0
          
          setFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, progress }
              : f
          ))
        }
      })

      if ((response.data as any).success) {
        // Update status to processing
        setFiles(prev => prev.map(f => 
          f.id === uploadFile.id 
            ? { 
                ...f, 
                status: 'processing' as const, 
                progress: 100,
                designId: (response.data as any).data.id 
              }
            : f
        ))

        // Simulate processing time
        setTimeout(() => {
          setFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, status: 'completed' as const }
              : f
          ))

          // Emit WebSocket event for real-time updates
          emit('design:uploaded', {
            designId: (response.data as any).data.id,
            name: formData.name,
            category: formData.category
          })

          success(`Design "${formData.name}" uploaded successfully!`)
        }, 2000)

      } else {
        throw new Error((response.data as any).error || 'Upload failed')
      }
    } catch (err: any) {
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { 
              ...f, 
              status: 'error' as const, 
              error: err.response?.data?.error || err.message || 'Upload failed'
            }
          : f
      ))
      error(`Failed to upload ${uploadFile.file.name}`)
    }
  }

  const onSubmit = async (formData: DesignFormData) => {
    if (files.length === 0) {
      error('Please select at least one file to upload')
      return
    }

    setIsUploading(true)

    try {
      // Upload files sequentially to avoid overwhelming the server
      for (const file of files.filter(f => f.status === 'pending')) {
        await uploadFile(file, formData)
      }
    } finally {
      setIsUploading(false)
    }
  }

  const retryUpload = async (fileId: string) => {
    const file = files.find(f => f.id === fileId)
    if (!file) return

    const formData = {
      name: watch('name'),
      description: watch('description'),
      category: watch('category'),
      tags: watch('tags'),
      price: watch('price'),
      is_public: watch('is_public')
    }

    await uploadFile(file, formData)
  }

  const clearCompleted = () => {
    setFiles(prev => {
      const toRemove = prev.filter(f => f.status === 'completed')
      toRemove.forEach(f => {
        if (f.preview) {
          URL.revokeObjectURL(f.preview)
        }
      })
      return prev.filter(f => f.status !== 'completed')
    })
  }

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return <Upload className="h-4 w-4 text-muted-foreground" />
      case 'uploading':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-orange-500 animate-spin" />
      case 'completed':
        return <Check className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusBadge = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">Pending</Badge>
      case 'uploading':
        return <Badge variant="default" className="bg-blue-500">Uploading</Badge>
      case 'processing':
        return <Badge variant="default" className="bg-orange-500">Processing</Badge>
      case 'completed':
        return <Badge variant="default" className="bg-green-500">Completed</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center">
            <Upload className="h-5 w-5 mr-2" />
            Design Upload
          </h3>
          <p className="text-sm text-muted-foreground">
            Upload your designs with real-time progress tracking
          </p>
        </div>
        {files.some(f => f.status === 'completed') && (
          <Button variant="outline" size="sm" onClick={clearCompleted}>
            <Trash2 className="h-4 w-4 mr-1" />
            Clear Completed
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Form */}
        <Card>
          <CardHeader>
            <CardTitle>Design Information</CardTitle>
            <CardDescription>
              Provide details about your design
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <Label htmlFor="name">Design Name</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter design name"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Describe your design"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select onValueChange={(value) => setValue('category', value)}>
                  <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="t-shirts">T-Shirts</SelectItem>
                    <SelectItem value="hoodies">Hoodies</SelectItem>
                    <SelectItem value="mugs">Mugs</SelectItem>
                    <SelectItem value="stickers">Stickers</SelectItem>
                    <SelectItem value="posters">Posters</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-red-500 mt-1">{errors.category.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <Input
                  id="tags"
                  {...register('tags')}
                  placeholder="vintage, logo, minimalist"
                />
              </div>

              <div>
                <Label htmlFor="price">Price ($)</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('price', { valueAsNumber: true })}
                  placeholder="0.00"
                  className={errors.price ? 'border-red-500' : ''}
                />
                {errors.price && (
                  <p className="text-sm text-red-500 mt-1">{errors.price.message}</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_public"
                  {...register('is_public')}
                  className="rounded"
                />
                <Label htmlFor="is_public">Make design public</Label>
              </div>

              <Button 
                type="submit" 
                disabled={isUploading || files.length === 0}
                className="w-full"
              >
                {isUploading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Designs
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* File Upload Area */}
        <Card>
          <CardHeader>
            <CardTitle>Files</CardTitle>
            <CardDescription>
              Drag and drop your design files or click to browse
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Dropzone */}
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive 
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' 
                  : 'border-muted-foreground/25 hover:border-muted-foreground/50'
              }`}
            >
              <input {...getInputProps()} ref={fileInputRef} />
              <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              {isDragActive ? (
                <p className="text-blue-600 dark:text-blue-400">Drop the files here...</p>
              ) : (
                <div>
                  <p className="text-muted-foreground mb-2">
                    Drag & drop design files here, or click to select
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Supports: JPG, PNG, GIF, WebP, SVG (max 10MB)
                  </p>
                </div>
              )}
            </div>

            {/* File List */}
            {files.length > 0 && (
              <div className="mt-6 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Selected Files ({files.length})</h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add More
                  </Button>
                </div>
                
                {files.map((file) => (
                  <div key={file.id} className="border rounded-lg p-3">
                    <div className="flex items-start space-x-3">
                      {/* Preview */}
                      <div className="flex-shrink-0">
                        <img
                          src={file.preview}
                          alt={file.file.name}
                          className="w-12 h-12 object-cover rounded"
                        />
                      </div>

                      {/* File Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium truncate">
                              {file.file.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {formatFileSize(file.file.size)}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(file.status)}
                            {getStatusBadge(file.status)}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(file.id)}
                              disabled={file.status === 'uploading' || file.status === 'processing'}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Progress Bar */}
                        {(file.status === 'uploading' || file.status === 'processing') && (
                          <div className="mt-2">
                            <Progress value={file.progress} className="h-2" />
                            <p className="text-xs text-muted-foreground mt-1">
                              {file.status === 'uploading' ? 'Uploading' : 'Processing'} {file.progress}%
                            </p>
                          </div>
                        )}

                        {/* Error Message */}
                        {file.status === 'error' && file.error && (
                          <div className="mt-2">
                            <p className="text-xs text-red-500">{file.error}</p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => retryUpload(file.id)}
                              className="mt-1"
                            >
                              <RefreshCw className="h-3 w-3 mr-1" />
                              Retry
                            </Button>
                          </div>
                        )}

                        {/* Success Actions */}
                        {file.status === 'completed' && file.designId && (
                          <div className="mt-2 flex items-center space-x-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                            <Button variant="outline" size="sm">
                              <Download className="h-3 w-3 mr-1" />
                              Download
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
