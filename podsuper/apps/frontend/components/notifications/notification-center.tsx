'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem, 
  DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { 
  Bell, BellRing, Check, X, Trash2, Settings, Filter,
  ShoppingCart, Package, Users, DollarSign, AlertTriangle,
  Info, CheckCircle, XCircle, Clock, MoreHorizontal
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import api from '@/lib/api'
import { useUIStore } from '@/stores/ui-store'

interface Notification {
  id: string
  type: 'order' | 'design' | 'payment' | 'system' | 'user' | 'alert'
  title: string
  message: string
  read: boolean
  priority: 'low' | 'medium' | 'high' | 'urgent'
  created_at: string
  data?: any
  action_url?: string
}

const notificationIcons = {
  order: ShoppingCart,
  design: Package,
  payment: DollarSign,
  system: Settings,
  user: Users,
  alert: AlertTriangle
}

const priorityColors = {
  low: 'bg-gray-500',
  medium: 'bg-blue-500',
  high: 'bg-orange-500',
  urgent: 'bg-red-500'
}

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const { success, error } = useUIStore()

  useEffect(() => {
    fetchNotifications()
  }, [])

  const fetchNotifications = async () => {
    try {
      setLoading(true)
      const response = await api.get('/notifications')
      setNotifications((response.data as any).data || generateMockNotifications())
    } catch (err) {
      console.error('Failed to fetch notifications:', err)
      // Use mock data for demo
      setNotifications(generateMockNotifications())
    } finally {
      setLoading(false)
    }
  }

  const generateMockNotifications = (): Notification[] => [
    {
      id: '1',
      type: 'order',
      title: 'New Order Received',
      message: 'Order #12345 from John Doe - Summer T-Shirt Design',
      read: false,
      priority: 'high',
      created_at: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
      data: { order_id: '12345', amount: 29.99 },
      action_url: '/orders/12345'
    },
    {
      id: '2',
      type: 'design',
      title: 'Design Approved',
      message: 'Your design "Vintage Logo Collection" has been approved and is now live',
      read: false,
      priority: 'medium',
      created_at: new Date(Date.now() - 900000).toISOString(), // 15 minutes ago
      data: { design_id: 'vintage-logo' },
      action_url: '/designs/vintage-logo'
    },
    {
      id: '3',
      type: 'payment',
      title: 'Payment Received',
      message: 'Payment of $450.75 has been processed successfully',
      read: true,
      priority: 'medium',
      created_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
      data: { amount: 450.75, transaction_id: 'txn_123' }
    },
    {
      id: '4',
      type: 'system',
      title: 'System Maintenance',
      message: 'Scheduled maintenance will occur tonight from 2-4 AM EST',
      read: false,
      priority: 'urgent',
      created_at: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    },
    {
      id: '5',
      type: 'user',
      title: 'New User Registration',
      message: 'Jane Smith has registered and is awaiting approval',
      read: true,
      priority: 'low',
      created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      data: { user_id: 'jane-smith' },
      action_url: '/admin/users'
    },
    {
      id: '6',
      type: 'alert',
      title: 'Low Stock Alert',
      message: 'T-Shirt inventory is running low (5 items remaining)',
      read: false,
      priority: 'high',
      created_at: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
      data: { product_type: 't-shirt', stock: 5 }
    }
  ]

  const filteredNotifications = notifications.filter(notification => {
    const matchesReadFilter = 
      filter === 'all' || 
      (filter === 'read' && notification.read) || 
      (filter === 'unread' && !notification.read)
    
    const matchesTypeFilter = typeFilter === 'all' || notification.type === typeFilter
    
    return matchesReadFilter && matchesTypeFilter
  })

  const unreadCount = notifications.filter(n => !n.read).length

  const markAsRead = async (notificationId: string) => {
    try {
      await api.patch(`/notifications/${notificationId}/read`)
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      )
    } catch (err) {
      // Fallback for demo
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      )
    }
  }

  const markAsUnread = async (notificationId: string) => {
    try {
      await api.patch(`/notifications/${notificationId}/unread`)
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: false } : n)
      )
    } catch (err) {
      // Fallback for demo
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: false } : n)
      )
    }
  }

  const deleteNotification = async (notificationId: string) => {
    try {
      await api.delete(`/notifications/${notificationId}`)
      setNotifications(prev => prev.filter(n => n.id !== notificationId))
      success('Notification deleted')
    } catch (err) {
      // Fallback for demo
      setNotifications(prev => prev.filter(n => n.id !== notificationId))
      success('Notification deleted')
    }
  }

  const markAllAsRead = async () => {
    try {
      await api.patch('/notifications/mark-all-read')
      setNotifications(prev => prev.map(n => ({ ...n, read: true })))
      success('All notifications marked as read')
    } catch (err) {
      // Fallback for demo
      setNotifications(prev => prev.map(n => ({ ...n, read: true })))
      success('All notifications marked as read')
    }
  }

  const clearAll = async () => {
    try {
      await api.delete('/notifications/clear-all')
      setNotifications([])
      success('All notifications cleared')
    } catch (err) {
      // Fallback for demo
      setNotifications([])
      success('All notifications cleared')
    }
  }

  const getPriorityBadge = (priority: string) => {
    const colorClass = priorityColors[priority as keyof typeof priorityColors]
    return (
      <div className={`w-2 h-2 rounded-full ${colorClass}`} title={`${priority} priority`} />
    )
  }

  const getNotificationIcon = (type: string) => {
    const IconComponent = notificationIcons[type as keyof typeof notificationIcons] || Bell
    return <IconComponent className="h-4 w-4" />
  }

  const formatTime = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), { addSuffix: true })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Bell className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Notification Center</h3>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="ml-2">
              {unreadCount}
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={markAllAsRead}>
            <Check className="h-4 w-4 mr-1" />
            Mark All Read
          </Button>
          <Button variant="outline" size="sm" onClick={clearAll}>
            <Trash2 className="h-4 w-4 mr-1" />
            Clear All
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <Tabs value={filter} onValueChange={(value) => setFilter(value as any)}>
              <TabsList>
                <TabsTrigger value="all">All ({notifications.length})</TabsTrigger>
                <TabsTrigger value="unread">Unread ({unreadCount})</TabsTrigger>
                <TabsTrigger value="read">Read ({notifications.length - unreadCount})</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <select 
              value={typeFilter} 
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-1 border rounded-md text-sm"
            >
              <option value="all">All Types</option>
              <option value="order">Orders</option>
              <option value="design">Designs</option>
              <option value="payment">Payments</option>
              <option value="system">System</option>
              <option value="user">Users</option>
              <option value="alert">Alerts</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle>Notifications</CardTitle>
          <CardDescription>
            Stay updated with the latest activities and alerts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="text-center py-8">
              <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No notifications found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`flex items-start space-x-3 p-4 rounded-lg border transition-colors ${
                    notification.read 
                      ? 'bg-background border-border' 
                      : 'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800'
                  }`}
                >
                  {/* Icon */}
                  <div className={`p-2 rounded-full ${
                    notification.read ? 'bg-muted' : 'bg-blue-100 dark:bg-blue-900'
                  }`}>
                    {getNotificationIcon(notification.type)}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className={`text-sm font-medium ${
                            notification.read ? 'text-muted-foreground' : 'text-foreground'
                          }`}>
                            {notification.title}
                          </h4>
                          {getPriorityBadge(notification.priority)}
                          <Badge variant="outline" className="text-xs">
                            {notification.type}
                          </Badge>
                        </div>
                        <p className={`text-sm mt-1 ${
                          notification.read ? 'text-muted-foreground' : 'text-foreground'
                        }`}>
                          {notification.message}
                        </p>
                        <p className="text-xs text-muted-foreground mt-2">
                          {formatTime(notification.created_at)}
                        </p>
                      </div>

                      {/* Actions */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          {notification.read ? (
                            <DropdownMenuItem onClick={() => markAsUnread(notification.id)}>
                              <BellRing className="h-4 w-4 mr-2" />
                              Mark as Unread
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem onClick={() => markAsRead(notification.id)}>
                              <Check className="h-4 w-4 mr-2" />
                              Mark as Read
                            </DropdownMenuItem>
                          )}
                          {notification.action_url && (
                            <DropdownMenuItem>
                              <Info className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => deleteNotification(notification.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    {/* Action Button */}
                    {notification.action_url && !notification.read && (
                      <div className="mt-3">
                        <Button size="sm" variant="outline">
                          View Details
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Notification Settings
          </CardTitle>
          <CardDescription>
            Configure your notification preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-muted-foreground">Receive notifications via email</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Push Notifications</p>
                <p className="text-sm text-muted-foreground">Receive browser push notifications</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Order Notifications</p>
                <p className="text-sm text-muted-foreground">Get notified about new orders</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">System Alerts</p>
                <p className="text-sm text-muted-foreground">Important system notifications</p>
              </div>
              <input type="checkbox" defaultChecked className="rounded" />
            </div>
          </div>
          <div className="mt-6">
            <Button>Save Preferences</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
