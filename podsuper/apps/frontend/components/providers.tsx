'use client'

import * as React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ToastProvider } from '@/components/ui/toast'
import { Toaster } from '@/components/ui/toaster'
import { useAuthStore } from '@/stores/auth-store'
import { useUIStore } from '@/stores/ui-store'
import api from '@/lib/api'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      retry: (failureCount, error: any) => {
        // Don't retry on 401/403 errors
        if (error?.response?.status === 401 || error?.response?.status === 403) {
          return false
        }
        return failureCount < 3
      },
    },
  },
})

interface ProvidersProps {
  children: React.ReactNode
}

// Theme initialization component
function ThemeInitializer({ children }: { children: React.ReactNode }) {
  const { theme } = useUIStore()
  const [isInitialized, setIsInitialized] = React.useState(false)

  React.useEffect(() => {
    // Initialize theme on mount only once
    if (!isInitialized) {
      console.log('🎨 Initializing theme:', theme)
      setIsInitialized(true)
    }
  }, [theme, isInitialized]) // Only run when theme changes or not initialized

  return <>{children}</>
}

// Auth initialization component
function AuthInitializer({ children }: { children: React.ReactNode }) {
  const { token } = useAuthStore()

  React.useEffect(() => {
    // Initialize API client with stored token
    const storedToken = localStorage.getItem('auth_token')
    if (storedToken) {
      api.setToken(storedToken)
    }
  }, [])

  React.useEffect(() => {
    // Update API client when token changes
    if (token) {
      api.setToken(token)
    } else {
      api.clearTokens()
    }
  }, [token])

  return <>{children}</>
}

export function Providers({ children }: ProvidersProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeInitializer>
        <ToastProvider>
          <AuthInitializer>
            {children}
          </AuthInitializer>
          <Toaster />
        </ToastProvider>
      </ThemeInitializer>
    </QueryClientProvider>
  )
}
