'use client'

import { useEffect, useState } from 'react'
import { 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts'
import api from '@/lib/api'
import { useUIStore } from '@/stores/ui-store'
import { OrderStatus } from '@repo/types'

interface OrdersChartProps {
  dateRange: {
    from: Date
    to: Date
  }
}

interface OrdersData {
  date: string
  pending: number
  approved: number
  processing: number
  shipped: number
  delivered: number
  cancelled: number
}

interface OrderStatusData {
  status: string
  count: number
  percentage: number
}

export function OrdersChart({ dateRange }: OrdersChartProps) {
  const { addNotification } = useUIStore()
  const [ordersData, setOrdersData] = useState<OrdersData[]>([])
  const [statusData, setStatusData] = useState<OrderStatusData[]>([])
  const [loading, setLoading] = useState(true)
  const [chartType, setChartType] = useState<'timeline' | 'status'>('timeline')

  const fetchOrdersData = async () => {
    try {
      setLoading(true)
      
      // Fetch orders timeline data
      const timelineResponse = await api.get('/dashboard/orders-timeline', {
        params: {
          from: dateRange.from.toISOString(),
          to: dateRange.to.toISOString(),
          interval: 'day'
        }
      })
      
      // Fetch orders status data
      const statusResponse = await api.get('/dashboard/orders-status', {
        params: {
          from: dateRange.from.toISOString(),
          to: dateRange.to.toISOString()
        }
      })
      
      if ((timelineResponse.data as any).success) {
        setOrdersData((timelineResponse.data as any).data)
      }

      if ((statusResponse.data as any).success) {
        setStatusData((statusResponse.data as any).data)
      }
    } catch (error) {
      console.error('Failed to fetch orders data:', error)
      
      // Generate mock data for demo
      const mockTimelineData: OrdersData[] = []
      const mockStatusData: OrderStatusData[] = [
        { status: 'Pending', count: 45, percentage: 25 },
        { status: 'Approved', count: 32, percentage: 18 },
        { status: 'Processing', count: 28, percentage: 16 },
        { status: 'Shipped', count: 38, percentage: 21 },
        { status: 'Delivered', count: 25, percentage: 14 },
        { status: 'Cancelled', count: 12, percentage: 6 },
      ]
      
      const startDate = new Date(dateRange.from)
      const endDate = new Date(dateRange.to)
      
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        mockTimelineData.push({
          date: d.toISOString().split('T')[0],
          pending: Math.floor(Math.random() * 10) + 2,
          approved: Math.floor(Math.random() * 8) + 1,
          processing: Math.floor(Math.random() * 6) + 1,
          shipped: Math.floor(Math.random() * 12) + 3,
          delivered: Math.floor(Math.random() * 8) + 2,
          cancelled: Math.floor(Math.random() * 3),
        })
      }
      
      setOrdersData(mockTimelineData)
      setStatusData(mockStatusData)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrdersData()
  }, [dateRange])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
  }

  const statusColors = {
    'Pending': '#f59e0b',
    'Approved': '#10b981',
    'Processing': '#3b82f6',
    'Shipped': '#8b5cf6',
    'Delivered': '#06b6d4',
    'Cancelled': '#ef4444',
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium">{formatDate(label)}</p>
          <div className="space-y-1 mt-2">
            {payload.map((entry: any, index: number) => (
              <p key={index} style={{ color: entry.color }}>
                {entry.dataKey}: {entry.value}
              </p>
            ))}
          </div>
        </div>
      )
    }
    return null
  }

  const PieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium">{data.status}</p>
          <p className="text-sm">Count: {data.count}</p>
          <p className="text-sm">Percentage: {data.percentage}%</p>
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <div className="h-80 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Chart Type Toggle */}
      <div className="flex space-x-2">
        <button
          onClick={() => setChartType('timeline')}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
            chartType === 'timeline'
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
              : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
          }`}
        >
          Timeline
        </button>
        <button
          onClick={() => setChartType('status')}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
            chartType === 'status'
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
              : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100'
          }`}
        >
          Status Distribution
        </button>
      </div>

      {/* Chart */}
      <div className="h-80">
        {chartType === 'timeline' ? (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={ordersData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-gray-200 dark:stroke-gray-700" />
              <XAxis 
                dataKey="date" 
                tickFormatter={formatDate}
                className="text-gray-600 dark:text-gray-400"
              />
              <YAxis className="text-gray-600 dark:text-gray-400" />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar dataKey="pending" stackId="a" fill="#f59e0b" name="Pending" />
              <Bar dataKey="approved" stackId="a" fill="#10b981" name="Approved" />
              <Bar dataKey="processing" stackId="a" fill="#3b82f6" name="Processing" />
              <Bar dataKey="shipped" stackId="a" fill="#8b5cf6" name="Shipped" />
              <Bar dataKey="delivered" stackId="a" fill="#06b6d4" name="Delivered" />
              <Bar dataKey="cancelled" stackId="a" fill="#ef4444" name="Cancelled" />
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ status, percentage }) => `${status} (${percentage}%)`}
                outerRadius={100}
                fill="#8884d8"
                dataKey="count"
              >
                {statusData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={statusColors[entry.status as keyof typeof statusColors]} 
                  />
                ))}
              </Pie>
              <Tooltip content={<PieTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        )}
      </div>
    </div>
  )
}
