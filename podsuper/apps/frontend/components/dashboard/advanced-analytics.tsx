'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  AreaChart, Area, BarChart, Bar, LineChart, Line, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  <PERSON>atter<PERSON><PERSON>, <PERSON>att<PERSON>, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar
} from 'recharts'
import { 
  TrendingUp, TrendingDown, Users, ShoppingCart, Package, 
  DollarSign, Calendar, Filter, Download, RefreshCw
} from 'lucide-react'
import api from '@/lib/api'

interface AdvancedMetrics {
  revenue: {
    total: number
    growth: number
    forecast: number
    breakdown: Array<{
      period: string
      revenue: number
      orders: number
      aov: number
      conversion: number
    }>
  }
  customers: {
    total: number
    new: number
    returning: number
    churn: number
    lifetime_value: number
    segments: Array<{
      segment: string
      count: number
      value: number
      growth: number
    }>
  }
  products: {
    total_designs: number
    active_designs: number
    top_performers: Array<{
      design_id: string
      name: string
      revenue: number
      orders: number
      conversion: number
    }>
    categories: Array<{
      category: string
      revenue: number
      orders: number
      growth: number
    }>
  }
  performance: {
    conversion_rate: number
    avg_order_value: number
    fulfillment_time: number
    customer_satisfaction: number
    return_rate: number
    profit_margin: number
  }
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']

export function AdvancedAnalytics() {
  const [metrics, setMetrics] = useState<AdvancedMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    fetchAdvancedMetrics()
  }, [timeRange])

  const fetchAdvancedMetrics = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/dashboard/advanced-analytics?range=${timeRange}`)
      setMetrics((response.data as any).data)
    } catch (error) {
      console.error('Failed to fetch advanced metrics:', error)
      // Use mock data for demo
      setMetrics(generateMockMetrics())
    } finally {
      setLoading(false)
    }
  }

  const generateMockMetrics = (): AdvancedMetrics => ({
    revenue: {
      total: 125430,
      growth: 18.5,
      forecast: 145000,
      breakdown: Array.from({ length: 30 }, (_, i) => ({
        period: `Day ${i + 1}`,
        revenue: Math.random() * 5000 + 2000,
        orders: Math.floor(Math.random() * 50 + 20),
        aov: Math.random() * 100 + 50,
        conversion: Math.random() * 5 + 2
      }))
    },
    customers: {
      total: 2847,
      new: 342,
      returning: 2505,
      churn: 5.2,
      lifetime_value: 287.50,
      segments: [
        { segment: 'VIP', count: 156, value: 15420, growth: 12.3 },
        { segment: 'Regular', count: 1890, value: 78450, growth: 8.7 },
        { segment: 'New', count: 801, value: 31560, growth: 25.1 }
      ]
    },
    products: {
      total_designs: 234,
      active_designs: 189,
      top_performers: [
        { design_id: '1', name: 'Summer Vibes T-Shirt', revenue: 12450, orders: 234, conversion: 8.5 },
        { design_id: '2', name: 'Vintage Logo Hoodie', revenue: 9870, orders: 156, conversion: 6.2 },
        { design_id: '3', name: 'Minimalist Mug', revenue: 7650, orders: 345, conversion: 12.1 }
      ],
      categories: [
        { category: 'T-Shirts', revenue: 45230, orders: 892, growth: 15.2 },
        { category: 'Hoodies', revenue: 32100, orders: 234, growth: 8.7 },
        { category: 'Mugs', revenue: 18900, orders: 567, growth: 22.1 },
        { category: 'Stickers', revenue: 12400, orders: 1234, growth: 5.3 }
      ]
    },
    performance: {
      conversion_rate: 7.8,
      avg_order_value: 67.50,
      fulfillment_time: 3.2,
      customer_satisfaction: 4.6,
      return_rate: 2.1,
      profit_margin: 34.5
    }
  })

  const formatCurrency = (value: number) => `$${value.toLocaleString()}`
  const formatPercent = (value: number) => `${value.toFixed(1)}%`

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Advanced Analytics</h3>
          <div className="animate-spin">
            <RefreshCw className="h-4 w-4" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!metrics) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Advanced Analytics</h3>
          <p className="text-sm text-muted-foreground">
            Detailed insights and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <Button variant="outline" size="sm" onClick={fetchAdvancedMetrics}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Conversion Rate</p>
                <p className="text-2xl font-bold">{formatPercent(metrics.performance.conversion_rate)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Order Value</p>
                <p className="text-2xl font-bold">{formatCurrency(metrics.performance.avg_order_value)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Customer LTV</p>
                <p className="text-2xl font-bold">{formatCurrency(metrics.customers.lifetime_value)}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Fulfillment Time</p>
                <p className="text-2xl font-bold">{metrics.performance.fulfillment_time}d</p>
              </div>
              <Package className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Satisfaction</p>
                <p className="text-2xl font-bold">{metrics.performance.customer_satisfaction}/5</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Profit Margin</p>
                <p className="text-2xl font-bold">{formatPercent(metrics.performance.profit_margin)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>Daily revenue over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={metrics.revenue.breakdown.slice(-14)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis tickFormatter={formatCurrency} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Area type="monotone" dataKey="revenue" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Customer Segments */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Segments</CardTitle>
                <CardDescription>Revenue by customer segment</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={metrics.customers.segments}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ segment, value }) => `${segment}: ${formatCurrency(value)}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {metrics.customers.segments.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue vs Orders */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue vs Orders</CardTitle>
                <CardDescription>Correlation between revenue and order volume</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ScatterChart data={metrics.revenue.breakdown}>
                    <CartesianGrid />
                    <XAxis dataKey="orders" name="Orders" />
                    <YAxis dataKey="revenue" name="Revenue" tickFormatter={formatCurrency} />
                    <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                    <Scatter name="Revenue vs Orders" dataKey="revenue" fill="#3b82f6" />
                  </ScatterChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* AOV Trend */}
            <Card>
              <CardHeader>
                <CardTitle>Average Order Value</CardTitle>
                <CardDescription>AOV trend over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={metrics.revenue.breakdown.slice(-14)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis tickFormatter={formatCurrency} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Line type="monotone" dataKey="aov" stroke="#10b981" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="customers" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Growth */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Acquisition</CardTitle>
                <CardDescription>New vs returning customers</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>New Customers</span>
                    <Badge variant="secondary">{metrics.customers.new}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Returning Customers</span>
                    <Badge variant="secondary">{metrics.customers.returning}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Churn Rate</span>
                    <Badge variant="destructive">{formatPercent(metrics.customers.churn)}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Segment Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Segment Performance</CardTitle>
                <CardDescription>Growth by customer segment</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={metrics.customers.segments}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="segment" />
                    <YAxis tickFormatter={formatPercent} />
                    <Tooltip formatter={(value) => formatPercent(Number(value))} />
                    <Bar dataKey="growth" fill="#8b5cf6" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Designs</CardTitle>
                <CardDescription>Best selling designs by revenue</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics.products.top_performers.map((design, index) => (
                    <div key={design.design_id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{design.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {design.orders} orders • {formatPercent(design.conversion)} conversion
                        </p>
                      </div>
                      <Badge variant="secondary">{formatCurrency(design.revenue)}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Category Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Category Performance</CardTitle>
                <CardDescription>Revenue by product category</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={metrics.products.categories}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis tickFormatter={formatCurrency} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Bar dataKey="revenue" fill="#f59e0b" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
