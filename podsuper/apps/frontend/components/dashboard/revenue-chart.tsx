'use client'

import { useEffect, useState } from 'react'
import { 
  ResponsiveContainer, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend,
  AreaChart,
  Area
} from 'recharts'
import api from '@/lib/api'
import { useUIStore } from '@/stores/ui-store'

interface RevenueChartProps {
  dateRange: {
    from: Date
    to: Date
  }
}

interface RevenueData {
  date: string
  revenue: number
  orders: number
  average_order_value: number
}

export function RevenueChart({ dateRange }: RevenueChartProps) {
  const { addNotification } = useUIStore()
  const [data, setData] = useState<RevenueData[]>([])
  const [loading, setLoading] = useState(true)

  const fetchRevenueData = async () => {
    try {
      setLoading(true)
      const response = await api.get('/dashboard/revenue-chart', {
        params: {
          from: dateRange.from.toISOString(),
          to: dateRange.to.toISOString(),
          interval: 'day'
        }
      })
      
      if ((response.data as any).success) {
        setData((response.data as any).data)
      }
    } catch (error) {
      console.error('Failed to fetch revenue data:', error)
      
      // Generate mock data for demo
      const mockData: RevenueData[] = []
      const startDate = new Date(dateRange.from)
      const endDate = new Date(dateRange.to)
      
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const baseRevenue = 1000 + Math.random() * 2000
        const orders = Math.floor(10 + Math.random() * 30)
        
        mockData.push({
          date: d.toISOString().split('T')[0],
          revenue: Math.round(baseRevenue),
          orders: orders,
          average_order_value: Math.round(baseRevenue / orders)
        })
      }
      
      setData(mockData)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRevenueData()
  }, [dateRange])

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="font-medium">{formatDate(label)}</p>
          <div className="space-y-1 mt-2">
            <p className="text-blue-600 dark:text-blue-400">
              Revenue: {formatCurrency(payload[0].value)}
            </p>
            <p className="text-green-600 dark:text-green-400">
              Orders: {payload[0].payload.orders}
            </p>
            <p className="text-purple-600 dark:text-purple-400">
              AOV: {formatCurrency(payload[0].payload.average_order_value)}
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <div className="h-80 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
              <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" className="stroke-gray-200 dark:stroke-gray-700" />
          <XAxis 
            dataKey="date" 
            tickFormatter={formatDate}
            className="text-gray-600 dark:text-gray-400"
          />
          <YAxis 
            tickFormatter={formatCurrency}
            className="text-gray-600 dark:text-gray-400"
          />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="revenue"
            stroke="#3b82f6"
            strokeWidth={2}
            fill="url(#revenueGradient)"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
