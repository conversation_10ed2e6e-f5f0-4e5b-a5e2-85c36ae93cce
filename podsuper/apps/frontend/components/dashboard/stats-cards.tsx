'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  TrendingDown, 
  Package, 
  ShoppingCart, 
  DollarSign, 
  Users,
  Activity,
  Palette
} from 'lucide-react'
import { DashboardStats } from '@repo/types'

interface StatsCardsProps {
  stats: DashboardStats | null
  loading: boolean
}

export function StatsCards({ stats, loading }: StatsCardsProps) {
  if (loading || !stats) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-24"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num)
  }

  const calculatePercentageChange = (current: number, previous: number) => {
    if (previous === 0) return 0
    return ((current - previous) / previous) * 100
  }

  // Mock previous period data for percentage calculations
  const previousStats = {
    total_revenue: stats.total_revenue * 0.85,
    total_orders: stats.total_orders * 0.92,
    total_designs: stats.total_designs * 0.78,
    connected_stores: stats.connected_stores * 0.95,
  }

  const revenueChange = calculatePercentageChange(stats.total_revenue, previousStats.total_revenue)
  const ordersChange = calculatePercentageChange(stats.total_orders, previousStats.total_orders)
  const designsChange = calculatePercentageChange(stats.total_designs, previousStats.total_designs)
  const storesChange = calculatePercentageChange(stats.connected_stores, previousStats.connected_stores)

  const statsData = [
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.total_revenue),
      change: revenueChange,
      icon: DollarSign,
      description: 'from last month',
    },
    {
      title: 'Total Orders',
      value: formatNumber(stats.total_orders),
      change: ordersChange,
      icon: ShoppingCart,
      description: 'from last month',
    },
    {
      title: 'Active Designs',
      value: formatNumber(stats.active_designs),
      change: designsChange,
      icon: Palette,
      description: 'from last month',
    },
    {
      title: 'Connected Stores',
      value: formatNumber(stats.connected_stores),
      change: storesChange,
      icon: Activity,
      description: 'Shopify stores',
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statsData.map((stat, index) => {
        const Icon = stat.icon
        const isPositive = stat.change >= 0
        
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {stat.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <div className="flex items-center">
                  {isPositive ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={isPositive ? 'text-green-500' : 'text-red-500'}>
                    {Math.abs(stat.change).toFixed(1)}%
                  </span>
                </div>
                <span>{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
