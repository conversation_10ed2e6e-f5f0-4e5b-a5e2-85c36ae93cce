'use client'

import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Package, 
  ShoppingCart, 
  CheckCircle, 
  Truck, 
  AlertCircle,
  Palette,
  Store,
  DollarSign
} from 'lucide-react'
import { RecentActivity } from '@repo/types'

interface RecentActivityListProps {
  activities: RecentActivity[]
}

export function RecentActivityList({ activities }: RecentActivityListProps) {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'design_uploaded':
        return <Palette className="h-4 w-4 text-blue-500" />
      case 'order_received':
        return <ShoppingCart className="h-4 w-4 text-green-500" />
      case 'order_approved':
        return <CheckCircle className="h-4 w-4 text-emerald-500" />
      case 'order_shipped':
        return <Truck className="h-4 w-4 text-purple-500" />
      case 'store_connected':
        return <Store className="h-4 w-4 text-indigo-500" />
      case 'payment_received':
        return <DollarSign className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getActivityBadge = (type: string) => {
    switch (type) {
      case 'design_uploaded':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-700">Design</Badge>
      case 'order_received':
        return <Badge variant="secondary" className="bg-green-100 text-green-700">Order</Badge>
      case 'order_approved':
        return <Badge variant="secondary" className="bg-emerald-100 text-emerald-700">Approved</Badge>
      case 'order_shipped':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-700">Shipped</Badge>
      case 'store_connected':
        return <Badge variant="secondary" className="bg-indigo-100 text-indigo-700">Store</Badge>
      case 'payment_received':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-700">Payment</Badge>
      default:
        return <Badge variant="secondary">Activity</Badge>
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const activityTime = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - activityTime.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    return activityTime.toLocaleDateString()
  }

  // Mock data if no activities provided
  const mockActivities: RecentActivity[] = [
    {
      id: '1',
      type: 'order_received',
      title: 'New order received',
      description: 'Order #12345 from John Doe - Summer T-Shirt Design',
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      metadata: { orderId: '12345', customerName: 'John Doe', amount: 29.99 }
    },
    {
      id: '2',
      type: 'design_uploaded',
      title: 'New design uploaded',
      description: 'Vintage Logo Collection - 5 new designs added',
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      metadata: { designCount: 5, collection: 'Vintage Logo' }
    },
    {
      id: '3',
      type: 'order_approved',
      title: 'Order approved',
      description: 'Order #12344 approved and sent to production',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      metadata: { orderId: '12344', productionTime: '2-3 days' }
    },
    {
      id: '4',
      type: 'payment_received',
      title: 'Payment received',
      description: '$156.75 payment confirmed for order #12343',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      metadata: { amount: 156.75, orderId: '12343' }
    },
    {
      id: '5',
      type: 'order_shipped',
      title: 'Order shipped',
      description: 'Order #12342 shipped via FedEx - Tracking: 1234567890',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      metadata: { orderId: '12342', carrier: 'FedEx', tracking: '1234567890' }
    },
    {
      id: '6',
      type: 'store_connected',
      title: 'New store connected',
      description: 'Awesome Apparel Store connected successfully',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      metadata: { storeName: 'Awesome Apparel Store', platform: 'Shopify' }
    }
  ]

  const displayActivities = activities.length > 0 ? activities : mockActivities

  if (displayActivities.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <AlertCircle className="h-8 w-8 text-gray-400 mb-2" />
        <p className="text-sm text-muted-foreground">No recent activity</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {displayActivities.map((activity) => (
        <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
          <div className="flex-shrink-0 mt-1">
            {getActivityIcon(activity.type)}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {activity.title}
              </p>
              {getActivityBadge(activity.type)}
            </div>
            
            <p className="text-sm text-muted-foreground mb-2">
              {activity.description}
            </p>
            
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                {formatTimeAgo(activity.timestamp)}
              </p>
              
              {activity.metadata && (
                <div className="flex items-center space-x-2">
                  {activity.metadata.amount && (
                    <span className="text-xs font-medium text-green-600 dark:text-green-400">
                      ${activity.metadata.amount}
                    </span>
                  )}
                  {activity.metadata.orderId && (
                    <span className="text-xs text-muted-foreground">
                      #{activity.metadata.orderId}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
      
      <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
        <button className="w-full text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
          View all activity
        </button>
      </div>
    </div>
  )
}
