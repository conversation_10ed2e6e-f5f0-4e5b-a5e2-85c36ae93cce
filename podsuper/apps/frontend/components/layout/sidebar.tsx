'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useSidebar } from '@/stores/ui-store'
import { useAuthStore } from '@/stores/auth-store'
import {
  LayoutDashboard,
  Palette,
  Package,
  ShoppingCart,
  Store,
  Truck,
  Bot,
  Settings,
  Shield,
  ChevronLeft,
  ChevronRight,
  Menu,
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Designs',
    href: '/designs',
    icon: Palette,
    badge: '12',
  },
  {
    name: 'AI Tools',
    href: '/ai',
    icon: <PERSON><PERSON>,
    children: [
      { name: 'Video Generator', href: '/ai/video' },
      { name: 'Mockup Generator', href: '/ai/mockups' },
    ],
  },
  {
    name: 'Orders',
    href: '/orders',
    icon: ShoppingCart,
    badge: '5',
  },
  {
    name: 'Printify',
    href: '/printify',
    icon: Package,
  },
  {
    name: 'Shopify Stores',
    href: '/shopify',
    icon: Store,
  },
  {
    name: 'Fulfillment',
    href: '/fulfillment',
    icon: Truck,
  },
  {
    name: 'Admin',
    href: '/admin',
    icon: Shield,
    adminOnly: true,
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Settings,
  },
]

export function Sidebar() {
  const pathname = usePathname()
  const { user } = useAuthStore()
  const { isOpen, isCollapsed, setOpen, toggleCollapsed } = useSidebar()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (name: string) => {
    setExpandedItems(prev =>
      prev.includes(name)
        ? prev.filter(item => item !== name)
        : [...prev, name]
    )
  }

  return (
    <TooltipProvider>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300',
          isCollapsed ? 'w-16' : 'w-64',
          isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className={cn(
            "flex items-center space-x-2 transition-all duration-300",
            isCollapsed ? "opacity-0 w-0 overflow-hidden" : "opacity-100 w-auto"
          )}>
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
              <Palette className="w-5 h-5 text-white" />
            </div>
            <span className="font-semibold text-gray-900 dark:text-gray-100 whitespace-nowrap">
              Design System
            </span>
          </div>

          {isCollapsed && (
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center mx-auto">
              <Palette className="w-5 h-5 text-white" />
            </div>
          )}

          <div className={cn(
            "flex items-center space-x-1",
            isCollapsed && "absolute right-4"
          )}>
            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
              onClick={() => setOpen(false)}
            >
              <Menu className="w-5 h-5" />
            </Button>

            {/* Collapse button */}
            <Button
              variant="ghost"
              size="icon"
              className="hidden lg:flex text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
              onClick={toggleCollapsed}
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4" />
              ) : (
                <ChevronLeft className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {navigation
            .filter(item => !item.adminOnly || user?.role === 'admin')
            .map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
            const isExpanded = expandedItems.includes(item.name)
            const hasChildren = item.children && item.children.length > 0

            const menuItem = hasChildren ? (
              <button
                onClick={() => toggleExpanded(item.name)}
                className={cn(
                  'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                  isActive
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-[1.02]',
                  isCollapsed && 'justify-center'
                )}
              >
                <div className={cn(
                  "flex items-center transition-all duration-300",
                  isCollapsed ? "space-x-0" : "space-x-3"
                )}>
                  <item.icon className="w-5 h-5 flex-shrink-0" />
                  <span className={cn(
                    "transition-all duration-300",
                    isCollapsed ? "opacity-0 w-0 overflow-hidden" : "opacity-100 w-auto"
                  )}>
                    {item.name}
                  </span>
                </div>
                {!isCollapsed && (
                  <ChevronRight
                    className={cn(
                      'w-4 h-4 transition-transform duration-200',
                      isExpanded && 'rotate-90'
                    )}
                  />
                )}
              </button>
            ) : (
              <Link
                href={item.href}
                className={cn(
                  'flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
                  isActive
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:scale-[1.02]',
                  isCollapsed && 'justify-center'
                )}
              >
                <div className={cn(
                  "flex items-center transition-all duration-300",
                  isCollapsed ? "space-x-0" : "space-x-3"
                )}>
                  <item.icon className="w-5 h-5 flex-shrink-0" />
                  <span className={cn(
                    "transition-all duration-300",
                    isCollapsed ? "opacity-0 w-0 overflow-hidden" : "opacity-100 w-auto"
                  )}>
                    {item.name}
                  </span>
                </div>
                {!isCollapsed && item.badge && (
                  <Badge variant="secondary" className="ml-auto transition-all duration-300">
                    {item.badge}
                  </Badge>
                )}
              </Link>
            )

            return (
              <div key={item.name}>
                {isCollapsed ? (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      {menuItem}
                    </TooltipTrigger>
                    <TooltipContent side="right" className="flex items-center gap-2">
                      {item.name}
                      {item.badge && (
                        <Badge variant="secondary" className="text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </TooltipContent>
                  </Tooltip>
                ) : (
                  menuItem
                )}

                {/* Submenu */}
                {hasChildren && isExpanded && !isCollapsed && (
                  <div className="ml-8 mt-2 space-y-1 animate-in slide-in-from-top-2 duration-200">
                    {item.children?.map((child) => (
                      <Link
                        key={child.name}
                        href={child.href}
                        className={cn(
                          'block px-3 py-2 text-sm text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 hover:scale-[1.02] hover:translate-x-1',
                          pathname === child.href && 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 scale-[1.02] translate-x-1'
                        )}
                      >
                        {child.name}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </nav>

        {/* Footer */}
        <div className={cn(
          "border-t border-gray-200 dark:border-gray-700 transition-all duration-300",
          isCollapsed ? "p-2" : "p-4"
        )}>
          <div className={cn(
            "text-xs text-gray-500 dark:text-gray-400 transition-all duration-300",
            isCollapsed ? "opacity-0 h-0 overflow-hidden" : "opacity-100 h-auto"
          )}>
            Design Fulfillment System v1.0
          </div>
          {isCollapsed && (
            <div className="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full mx-auto"></div>
          )}
        </div>
      </div>
    </TooltipProvider>
  )
}
