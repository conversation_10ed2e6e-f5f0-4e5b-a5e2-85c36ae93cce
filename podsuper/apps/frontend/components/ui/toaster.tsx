'use client'

import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/toast'
import { useNotifications } from '@/stores/ui-store'

export function Toaster() {
  const { notifications, removeNotification } = useNotifications()

  return (
    <ToastProvider>
      {notifications.map(function ({ id, title, message, type, action, ...props }) {
        return (
          <Toast key={id} variant={type === 'error' ? 'destructive' : type} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {message && <ToastDescription>{message}</ToastDescription>}
            </div>
            {action && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={action.onClick}
                  className="text-sm font-medium underline"
                >
                  {action.label}
                </button>
              </div>
            )}
            <ToastClose onClick={() => removeNotification(id)} />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
