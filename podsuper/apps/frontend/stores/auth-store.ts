import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, LoginCredentials, RegisterData } from '@repo/types'
import { authService } from '@/services/authService'

interface AuthState {
  // State
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // Actions
  login: (credentials: LoginCredentials) => Promise<boolean>
  register: (userData: RegisterData) => Promise<boolean>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<void>
  updateProfile: (userData: Partial<User>) => Promise<boolean>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Login action
      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null })

        try {
          const response = await authService.login(credentials)

          if (response.success && response.data) {
            set({
              user: response.data.user,
              token: response.data.token,
              isAuthenticated: true,
              isLoading: false,
              error: null
            })
            return true
          } else {
            set({
              error: response.error || 'Login failed',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || 'Login failed',
            isLoading: false
          })
          return false
        }
      },

      // Register action
      register: async (userData: RegisterData) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authService.register(userData)
          
          if (response.success && response.data) {
            set({
              user: response.data.user,
              token: response.data.token,
              isAuthenticated: true,
              isLoading: false,
              error: null
            })
            return true
          } else {
            set({
              error: response.error || 'Registration failed',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || 'Registration failed',
            isLoading: false
          })
          return false
        }
      },

      // Logout action
      logout: async () => {
        set({ isLoading: true })
        
        try {
          await authService.logout()
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
        }
      },

      // Get current user
      getCurrentUser: async () => {
        // Only fetch if we think we're authenticated but don't have user data
        if (authService.isAuthenticated() && !get().user) {
          set({ isLoading: true })
          
          try {
            const response = await authService.getCurrentUser()

            if (response.success && response.data) {
              set({
                user: response.data,
                isAuthenticated: true,
                isLoading: false
              })
            } else {
              // Token is invalid, clear auth state
              set({
                user: null,
                isAuthenticated: false,
                isLoading: false
              })
            }
          } catch (error) {
            console.error('Get current user error:', error)
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false
            })
          }
        }
      },

      // Update profile
      updateProfile: async (userData: Partial<User>) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await authService.updateProfile(userData)
          
          if (response.success && response.data) {
            set({
              user: response.data.user || response.data,
              isLoading: false,
              error: null
            })
            return true
          } else {
            set({
              error: response.error || 'Profile update failed',
              isLoading: false
            })
            return false
          }
        } catch (error: any) {
          set({
            error: error.message || 'Profile update failed',
            isLoading: false
          })
          return false
        }
      },

      // Clear error
      clearError: () => set({ error: null }),

      // Set loading
      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
