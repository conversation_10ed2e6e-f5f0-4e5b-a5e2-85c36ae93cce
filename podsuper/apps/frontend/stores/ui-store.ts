import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface UIState {
  // Sidebar state
  sidebarOpen: boolean
  sidebarCollapsed: boolean
  
  // Theme state
  theme: 'light' | 'dark' | 'system'
  
  // Loading states
  globalLoading: boolean
  
  // Notification state
  notifications: Notification[]
  
  // Modal states
  modals: Record<string, boolean>
  
  // Actions
  toggleSidebar: () => void
  setSidebarOpen: (open: boolean) => void
  toggleSidebarCollapsed: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setGlobalLoading: (loading: boolean) => void
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
  success: (title: string, message?: string) => void
  error: (title: string, message?: string) => void
  openModal: (modalId: string) => void
  closeModal: (modalId: string) => void
  toggleModal: (modalId: string) => void
}

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  timestamp: number
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // Initial state
      sidebarOpen: true,
      sidebarCollapsed: false,
      theme: 'system',
      globalLoading: false,
      notifications: [],
      modals: {},

      // Sidebar actions
      toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
      
      setSidebarOpen: (open: boolean) => set({ sidebarOpen: open }),
      
      toggleSidebarCollapsed: () => set((state) => ({ 
        sidebarCollapsed: !state.sidebarCollapsed 
      })),
      
      setSidebarCollapsed: (collapsed: boolean) => set({ sidebarCollapsed: collapsed }),

      // Theme actions
      setTheme: (theme: 'light' | 'dark' | 'system') => {
        set({ theme })
        
        // Apply theme to document
        if (typeof window !== 'undefined') {
          const root = window.document.documentElement
          
          if (theme === 'dark') {
            root.classList.add('dark')
          } else if (theme === 'light') {
            root.classList.remove('dark')
          } else {
            // System theme
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
            if (systemTheme) {
              root.classList.add('dark')
            } else {
              root.classList.remove('dark')
            }
          }
        }
      },

      // Loading actions
      setGlobalLoading: (loading: boolean) => set({ globalLoading: loading }),

      // Notification actions
      addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => {
        const id = Math.random().toString(36).substring(2)
        const timestamp = Date.now()
        
        const newNotification: Notification = {
          ...notification,
          id,
          timestamp,
          duration: notification.duration || 5000,
        }
        
        set((state) => ({
          notifications: [...state.notifications, newNotification]
        }))
        
        // Auto remove notification after duration
        if (newNotification.duration && newNotification.duration > 0) {
          setTimeout(() => {
            get().removeNotification(id)
          }, newNotification.duration)
        }
      },
      
      removeNotification: (id: string) => set((state) => ({
        notifications: state.notifications.filter(n => n.id !== id)
      })),
      
      clearNotifications: () => set({ notifications: [] }),

      // Helper methods for common notification types
      success: (title: string, message?: string) => {
        get().addNotification({
          type: 'success',
          title,
          message,
          duration: 5000
        })
      },

      error: (title: string, message?: string) => {
        get().addNotification({
          type: 'error',
          title,
          message,
          duration: 8000
        })
      },

      // Modal actions
      openModal: (modalId: string) => set((state) => ({
        modals: { ...state.modals, [modalId]: true }
      })),
      
      closeModal: (modalId: string) => set((state) => ({
        modals: { ...state.modals, [modalId]: false }
      })),
      
      toggleModal: (modalId: string) => set((state) => ({
        modals: { ...state.modals, [modalId]: !state.modals[modalId] }
      })),
    }),
    {
      name: 'ui-store',
      partialize: (state) => ({
        sidebarCollapsed: state.sidebarCollapsed,
        theme: state.theme,
      }),
    }
  )
)

// Helper hooks for specific UI states
export const useSidebar = () => {
  const { 
    sidebarOpen, 
    sidebarCollapsed, 
    toggleSidebar, 
    setSidebarOpen, 
    toggleSidebarCollapsed, 
    setSidebarCollapsed 
  } = useUIStore()
  
  return {
    isOpen: sidebarOpen,
    isCollapsed: sidebarCollapsed,
    toggle: toggleSidebar,
    setOpen: setSidebarOpen,
    toggleCollapsed: toggleSidebarCollapsed,
    setCollapsed: setSidebarCollapsed,
  }
}

export const useTheme = () => {
  const { theme, setTheme } = useUIStore()
  
  return {
    theme,
    setTheme,
    isDark: theme === 'dark' || (theme === 'system' && 
      typeof window !== 'undefined' && 
      window.matchMedia('(prefers-color-scheme: dark)').matches
    ),
  }
}

export const useNotifications = () => {
  const { 
    notifications, 
    addNotification, 
    removeNotification, 
    clearNotifications 
  } = useUIStore()
  
  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    // Convenience methods
    success: (title: string, message?: string) => 
      addNotification({ type: 'success', title, message }),
    error: (title: string, message?: string) => 
      addNotification({ type: 'error', title, message }),
    warning: (title: string, message?: string) => 
      addNotification({ type: 'warning', title, message }),
    info: (title: string, message?: string) => 
      addNotification({ type: 'info', title, message }),
  }
}

export const useModal = (modalId: string) => {
  const { modals, openModal, closeModal, toggleModal } = useUIStore()
  
  return {
    isOpen: !!modals[modalId],
    open: () => openModal(modalId),
    close: () => closeModal(modalId),
    toggle: () => toggleModal(modalId),
  }
}
