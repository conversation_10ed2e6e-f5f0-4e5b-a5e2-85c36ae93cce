import { User, LoginCredentials, RegisterData, AuthResponse } from '@repo/types'
import apiClient from './api'

export class AuthService {
  // Login user
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<{ user: User; token: string }>('/auth/login', credentials)
      
      if (response.success && response.data) {
        // Store token
        apiClient.setToken(response.data.token)
        
        // Store refresh token if provided
        if ('refresh_token' in response.data) {
          apiClient.setRefreshToken((response.data as any).refresh_token)
        }
        
        return {
          success: true,
          data: response.data,
          message: response.message || 'Login successful'
        }
      }
      
      return {
        success: false,
        error: response.error || 'Login failed'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Login failed'
      }
    }
  }

  // Register user
  async register(userData: RegisterData): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<{ user: User; token: string }>('/auth/register', userData)
      
      if (response.success && response.data) {
        // Store token
        apiClient.setToken(response.data.token)
        
        // Store refresh token if provided
        if ('refresh_token' in response.data) {
          apiClient.setRefreshToken((response.data as any).refresh_token)
        }
        
        return {
          success: true,
          data: response.data,
          message: response.message || 'Registration successful'
        }
      }
      
      return {
        success: false,
        error: response.error || 'Registration failed'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Registration failed'
      }
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      // Call logout endpoint to invalidate token on server
      await apiClient.post('/auth/logout')
    } catch (error) {
      // Continue with logout even if server call fails
      console.error('Logout error:', error)
    } finally {
      // Clear tokens from storage
      apiClient.clearTokens()
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await apiClient.get<User>('/auth/me')
      
      if (response.success && response.data) {
        return response.data
      }
      
      return null
    } catch (error) {
      console.error('Get current user error:', error)
      return null
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!apiClient.getToken()
  }

  // Forgot password
  async forgotPassword(email: string): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const response = await apiClient.post('/auth/forgot-password', { email })
      
      return {
        success: response.success,
        message: response.message || 'Password reset email sent'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to send reset email'
      }
    }
  }

  // Reset password
  async resetPassword(
    token: string, 
    password: string
  ): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const response = await apiClient.post('/auth/reset-password', { token, password })
      
      return {
        success: response.success,
        message: response.message || 'Password reset successful'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Password reset failed'
      }
    }
  }

  // Change password
  async changePassword(
    currentPassword: string, 
    newPassword: string
  ): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const response = await apiClient.post('/auth/change-password', {
        current_password: currentPassword,
        new_password: newPassword
      })
      
      return {
        success: response.success,
        message: response.message || 'Password changed successfully'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Password change failed'
      }
    }
  }

  // Update profile
  async updateProfile(userData: Partial<User>): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      const response = await apiClient.put<User>('/auth/profile', userData)
      
      if (response.success && response.data) {
        return {
          success: true,
          user: response.data
        }
      }
      
      return {
        success: false,
        error: response.error || 'Profile update failed'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Profile update failed'
      }
    }
  }

  // Verify email
  async verifyEmail(token: string): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const response = await apiClient.post('/auth/verify-email', { token })
      
      return {
        success: response.success,
        message: response.message || 'Email verified successfully'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Email verification failed'
      }
    }
  }

  // Resend verification email
  async resendVerificationEmail(): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const response = await apiClient.post('/auth/resend-verification')
      
      return {
        success: response.success,
        message: response.message || 'Verification email sent'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to send verification email'
      }
    }
  }
}

// Create singleton instance
export const authService = new AuthService()

// Export for use in components
export default authService
