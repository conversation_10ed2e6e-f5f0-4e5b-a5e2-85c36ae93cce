{"name": "frontend", "version": "1.0.0", "description": "Frontend for Design to Fulfillment System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.7", "@repo/config": "workspace:*", "@repo/types": "workspace:*", "@tanstack/react-query": "^5.17.0", "@types/js-cookie": "^3.0.6", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.6.0", "js-cookie": "^3.0.5", "lucide-react": "^0.300.0", "next": "^14.0.4", "next-themes": "^0.2.1", "postcss": "^8.4.32", "react": "^18.2.0", "react-day-picker": "^8.9.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.6", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3"}}