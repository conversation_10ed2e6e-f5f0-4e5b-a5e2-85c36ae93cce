# Multi-stage Dockerfile for Design Fulfillment System
# Supports both backend and frontend builds

# Base stage with common dependencies
FROM node:18-alpine AS base

# Install pnpm globally
RUN npm install -g pnpm

# Install system dependencies for backend image processing
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    curl

WORKDIR /app

# Copy workspace configuration
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY tsconfig.base.json ./

# Copy all package.json files
COPY packages/types/package.json ./packages/types/
COPY packages/config/package.json ./packages/config/
COPY packages/ui/package.json ./packages/ui/
COPY apps/backend/package.json ./apps/backend/
COPY apps/frontend/package.json ./apps/frontend/

# Install all dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY packages/ ./packages/
COPY apps/ ./apps/

# Build shared packages
RUN pnpm --filter @repo/types build
RUN pnpm --filter @repo/config build
RUN pnpm --filter @repo/ui build

# Backend production stage
FROM base AS backend-production

# Build backend
RUN pnpm --filter backend build

# Create production stage for backend
FROM node:18-alpine AS backend

# Install pnpm and system dependencies
RUN npm install -g pnpm && \
    apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    curl

WORKDIR /app

# Copy package files for production install
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY apps/backend/package.json ./apps/backend/
COPY packages/types/package.json ./packages/types/
COPY packages/config/package.json ./packages/config/

# Install production dependencies only
RUN pnpm install --frozen-lockfile --prod

# Copy built application from build stage
COPY --from=backend-production /app/packages/types/dist ./packages/types/dist
COPY --from=backend-production /app/packages/config/dist ./packages/config/dist
COPY --from=backend-production /app/apps/backend/dist ./apps/backend/dist
COPY --from=backend-production /app/apps/backend/migrations ./apps/backend/migrations

# Create logs directory and non-root user
RUN mkdir -p logs && \
    addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 && \
    chown -R nodejs:nodejs /app

USER nodejs

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

CMD ["pnpm", "--filter", "backend", "start"]

# Frontend production stage
FROM base AS frontend-production

# Build frontend
RUN pnpm --filter frontend build

# Create production stage for frontend
FROM node:18-alpine AS frontend

# Install pnpm
RUN npm install -g pnpm && \
    apk add --no-cache curl

WORKDIR /app

# Copy package files for production install
COPY package.json pnpm-workspace.yaml pnpm-lock.yaml ./
COPY apps/frontend/package.json ./apps/frontend/

# Install production dependencies only
RUN pnpm install --frozen-lockfile --prod

# Copy built application from build stage
COPY --from=frontend-production /app/apps/frontend/.next ./apps/frontend/.next
COPY --from=frontend-production /app/apps/frontend/public ./apps/frontend/public
COPY --from=frontend-production /app/apps/frontend/next.config.js ./apps/frontend/
COPY --from=frontend-production /app/apps/frontend/package.json ./apps/frontend/

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 && \
    chown -R nodejs:nodejs /app

USER nodejs

EXPOSE 3001

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/ || exit 1

CMD ["pnpm", "--filter", "frontend", "start"]
