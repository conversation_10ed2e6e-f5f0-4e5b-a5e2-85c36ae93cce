# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=design_fulfillment
DB_USER=postgres
DB_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your-r2-account-id
R2_ACCESS_KEY_ID=your-r2-access-key
R2_SECRET_ACCESS_KEY=your-r2-secret-key
R2_BUCKET_NAME=design-assets
R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com

# Kling AI Configuration
KLING_AI_API_KEY=your-kling-ai-api-key
KLING_AI_BASE_URL=https://api.kling.ai

# AI Mockup Configuration
MOCKUP_AI_API_KEY=your-mockup-ai-api-key
MOCKUP_AI_BASE_URL=https://api.mockup-ai.com

# Printify Configuration
PRINTIFY_API_KEY=your-printify-api-key
PRINTIFY_BASE_URL=https://api.printify.com/v1

# Shopify Configuration
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
SHOPIFY_WEBHOOK_SECRET=your-shopify-webhook-secret

# Gearment Configuration
GEARMENT_API_KEY=your-gearment-api-key
GEARMENT_BASE_URL=https://api.gearment.com/v1
GEARMENT_WEBHOOK_SECRET=your-gearment-webhook-secret

# File Upload Configuration
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/svg+xml,application/pdf
