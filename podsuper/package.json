{"name": "design-fulfillment-system", "version": "1.0.0", "description": "Design to Order Fulfillment System with AI Integration", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "type-check": "turbo run type-check", "test": "turbo run test", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "backend:dev": "pnpm --filter backend dev", "frontend:dev": "pnpm --filter frontend dev", "backend:build": "pnpm --filter backend build", "frontend:build": "pnpm --filter frontend build", "db:migrate": "pnpm --filter backend migrate", "db:setup": "pnpm --filter backend db:setup"}, "keywords": ["design", "fulfillment", "shopify", "printify", "gearment"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "turbo": "^1.11.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}