# Design to Fulfillment System

Complete design to fulfillment automation platform with AI integration, Printify, Shopify, and Gearment.

## 🏗️ Monorepo Structure

This project uses a monorepo architecture with pnpm workspaces and Turborepo for optimal development experience and build performance.

```
├── apps/
│   ├── backend/        # Node.js API server
│   └── frontend/       # Next.js web application
├── packages/           # Shared libraries
│   ├── config/         # Shared configuration
│   ├── types/          # TypeScript type definitions
│   └── ui/             # Shared UI components
├── .gitignore
├── package.json        # Root package with workspace scripts
├── tsconfig.base.json  # Base TypeScript configuration
├── turbo.json          # Turborepo configuration
└── pnpm-workspace.yaml # pnpm workspace configuration
```

## 🚀 Key Features

### 1. Design Management
- Upload and manage design files
- AI Kling integration for product video generation
- AI integration for realistic mockup creation
- Automatic storage on Cloudflare R2

### 2. Printify Integration
- Sync designs to Printify
- Retrieve mockups from Printify
- Store mockups on R2

### 3. Shopify Product Management
- Create and update products on Shopify
- Manage product information and variants
- Automatic webhooks from Shopify

### 4. Order Management
- Receive orders from Shopify webhooks
- Separate orders by shop
- Approve and process orders

### 5. Automatic Fulfillment
- Send approved orders to Gearment
- Track production and shipping status
- Update tracking for customers

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │  Admin Dashboard│    │   Mobile App    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Design Service  │    │ Order Service   │    │ AI Service      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    └─────────────────┘
```

## 🛠️ Technology Stack

### Backend (apps/backend)
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL with Knex.js
- **Cache**: Redis
- **Storage**: Cloudflare R2
- **Queue**: Bull (Redis-based)
- **Authentication**: JWT
- **Validation**: Joi
- **Logging**: Winston

### Frontend (apps/frontend)
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui + Radix UI
- **State Management**: Zustand
- **API Client**: Axios + React Query
- **Forms**: React Hook Form + Zod validation

### Shared Packages
- **@repo/types**: Shared TypeScript types
- **@repo/config**: Environment configuration
- **@repo/ui**: Shared UI components

### Development Tools
- **Monorepo**: pnpm workspaces + Turborepo
- **Linting**: ESLint + Prettier
- **Type Checking**: TypeScript strict mode

## 📦 Installation

### System Requirements
- Node.js >= 18.0.0
- pnpm >= 8.0.0
- PostgreSQL >= 13
- Redis >= 6.0

### Quick Start

1. **Clone repository**:
   ```bash
   git clone <repository-url>
   cd design-fulfillment-system
   ```

2. **Install dependencies**:
   ```bash
   pnpm install
   ```

3. **Environment setup**:
   ```bash
   # Backend environment
   cp apps/backend/.env.example apps/backend/.env

   # Frontend environment
   cp apps/frontend/.env.example apps/frontend/.env

   # Edit the .env files with your configuration
   ```

4. **Database setup**:
   ```bash
   # Run database migrations
   pnpm db:migrate
   ```

5. **Start development**:
   ```bash
   # Start all apps in development mode
   pnpm dev

   # Or start individually
   pnpm backend:dev    # Backend on http://localhost:3000
   pnpm frontend:dev   # Frontend on http://localhost:3001
   ```

### Production Build

```bash
# Build all apps
pnpm build

# Build individually
pnpm backend:build
pnpm frontend:build
```

## 🔧 Cấu hình

### Environment Variables

Xem file `.env.example` để biết danh sách đầy đủ các biến môi trường cần thiết.

### API Keys cần thiết:
- **Kling AI**: API key cho tạo video
- **Mockup AI**: API key cho tạo mockup
- **Printify**: API key và shop ID
- **Shopify**: API key, secret và webhook secret
- **Gearment**: API key và webhook secret
- **Cloudflare R2**: Account ID, access key và secret key

## 📚 API Documentation

### Authentication
Tất cả API endpoints (trừ webhooks) yêu cầu JWT token trong header:
```
Authorization: Bearer <your-jwt-token>
```

### Design Endpoints
- `POST /api/designs` - Upload thiết kế mới
- `GET /api/designs` - Lấy danh sách thiết kế
- `GET /api/designs/:id` - Lấy chi tiết thiết kế
- `PUT /api/designs/:id` - Cập nhật thiết kế
- `DELETE /api/designs/:id` - Xóa thiết kế
- `POST /api/designs/:id/ai-video` - Tạo video AI
- `POST /api/designs/:id/ai-mockups` - Tạo mockup AI

### Order Endpoints
- `GET /api/orders` - Lấy danh sách đơn hàng
- `GET /api/orders/:id` - Lấy chi tiết đơn hàng
- `POST /api/orders/:id/approve` - Duyệt đơn hàng
- `POST /api/orders/:id/cancel` - Hủy đơn hàng
- `GET /api/orders/stats` - Thống kê đơn hàng

### Webhook Endpoints
- `POST /api/webhooks/shopify/orders/create` - Shopify order created
- `POST /api/webhooks/shopify/orders/updated` - Shopify order updated
- `POST /api/webhooks/shopify/orders/paid` - Shopify order paid
- `POST /api/webhooks/shopify/orders/cancelled` - Shopify order cancelled
- `POST /api/webhooks/gearment/orders/update` - Gearment order update

## 🔄 Luồng xử lý

### 1. Tạo thiết kế
1. Designer upload file thiết kế
2. Hệ thống lưu file lên R2 và tạo thumbnail
3. Tùy chọn: Tạo video AI và mockup AI
4. Đồng bộ lên Printify để lấy mockup chính thức

### 2. Tạo sản phẩm
1. Cấu hình thông tin sản phẩm (title, description, price, variants)
2. Đẩy sản phẩm lên Shopify store
3. Sản phẩm sẵn sàng bán

### 3. Xử lý đơn hàng
1. Nhận webhook từ Shopify khi có đơn hàng mới
2. Lưu trữ đơn hàng và phân chia theo shop
3. Chờ thanh toán và duyệt đơn hàng
4. Gửi đơn hàng đã duyệt sang Gearment
5. Theo dõi trạng thái và cập nhật tracking

## 🧪 Testing

```bash
# Chạy tests
npm test

# Chạy tests với coverage
npm run test:coverage

# Chạy tests trong watch mode
npm run test:watch
```

## 📊 Monitoring & Logging

- Logs được lưu trong thư mục `logs/`
- Error logs: `logs/error.log`
- Combined logs: `logs/combined.log`
- Console logs trong development mode

## 🚀 Deployment

### Docker
```bash
# Build image
docker build -t design-fulfillment-system .

# Run container
docker run -p 3000:3000 --env-file .env design-fulfillment-system
```

### PM2
```bash
# Install PM2
npm install -g pm2

# Start application
pm2 start ecosystem.config.js

# Monitor
pm2 monit
```

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

Nếu bạn gặp vấn đề hoặc có câu hỏi, vui lòng tạo issue trên GitHub hoặc liên hệ team phát triển.
