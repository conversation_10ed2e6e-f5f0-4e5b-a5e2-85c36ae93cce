{"name": "@repo/config", "version": "1.0.0", "description": "Shared configuration for Design Fulfillment System", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"dotenv": "^16.3.1"}, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.10.5"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}