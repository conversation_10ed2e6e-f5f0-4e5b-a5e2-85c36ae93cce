import { config as dotenvConfig } from 'dotenv'

// Load environment variables
dotenvConfig()

export const config = {
  // Environment
  nodeEnv: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3000', 10),
  
  // Database
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    name: process.env.DB_NAME || 'design_fulfillment',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    ssl: process.env.DB_SSL === 'true',
  },
  
  // Redis
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
  },
  
  // JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  },
  
  // File Upload
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '********', 10), // 10MB
    allowedFileTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'application/pdf',
    ],
  },
  
  // Cloudflare R2
  r2: {
    accountId: process.env.R2_ACCOUNT_ID || '',
    accessKeyId: process.env.R2_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '',
    bucketName: process.env.R2_BUCKET_NAME || 'design-assets',
    endpoint: process.env.R2_ENDPOINT || '',
    publicUrl: process.env.R2_PUBLIC_URL || '',
  },
  
  // AI Services
  ai: {
    kling: {
      apiKey: process.env.KLING_AI_API_KEY || '',
      baseUrl: process.env.KLING_AI_BASE_URL || 'https://api.kling.ai',
    },
    mockup: {
      apiKey: process.env.MOCKUP_AI_API_KEY || '',
      baseUrl: process.env.MOCKUP_AI_BASE_URL || 'https://api.mockup.ai',
    },
  },
  
  // Printify
  printify: {
    apiKey: process.env.PRINTIFY_API_KEY || '',
    baseUrl: process.env.PRINTIFY_BASE_URL || 'https://api.printify.com/v1',
    shopId: process.env.PRINTIFY_SHOP_ID || '',
  },
  
  // Shopify
  shopify: {
    apiKey: process.env.SHOPIFY_API_KEY || '',
    apiSecret: process.env.SHOPIFY_API_SECRET || '',
    webhookSecret: process.env.SHOPIFY_WEBHOOK_SECRET || '',
    scopes: process.env.SHOPIFY_SCOPES || 'read_products,write_products,read_orders,write_orders',
  },
  
  // Gearment
  gearment: {
    apiKey: process.env.GEARMENT_API_KEY || '',
    baseUrl: process.env.GEARMENT_BASE_URL || 'https://api.gearment.com',
    webhookSecret: process.env.GEARMENT_WEBHOOK_SECRET || '',
  },
  
  // Email (for future use)
  email: {
    from: process.env.EMAIL_FROM || '<EMAIL>',
    smtp: {
      host: process.env.SMTP_HOST || '',
      port: parseInt(process.env.SMTP_PORT || '587', 10),
      user: process.env.SMTP_USER || '',
      password: process.env.SMTP_PASSWORD || '',
    },
  },
  
  // Frontend URLs
  frontend: {
    url: process.env.FRONTEND_URL || 'http://localhost:3001',
    dashboardUrl: process.env.DASHBOARD_URL || 'http://localhost:3001/dashboard',
  },
  
  // API URLs
  api: {
    baseUrl: process.env.API_BASE_URL || 'http://localhost:3000/api',
    webhookUrl: process.env.WEBHOOK_URL || 'http://localhost:3000/api/webhooks',
  },
  
  // WebSocket
  websocket: {
    port: parseInt(process.env.WS_PORT || '3001', 10),
    cors: {
      origin: process.env.WS_CORS_ORIGIN || 'http://localhost:3001',
    },
  },
}

// Validation function
export function validateConfig(): void {
  const requiredEnvVars = [
    'JWT_SECRET',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
  ]
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`)
  }
}

// Export individual configs for convenience
export const {
  nodeEnv,
  port,
  database,
  redis,
  jwt,
  upload,
  r2,
  ai,
  printify,
  shopify,
  gearment,
  email,
  frontend,
  api,
  websocket,
} = config
