{"name": "@repo/ui", "version": "1.0.0", "description": "Shared UI components for Design Fulfillment System", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"@repo/types": "workspace:*", "react": "^18.2.0", "react-dom": "^18.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.300.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}