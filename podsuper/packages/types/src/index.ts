// User and Authentication Types
export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'user' | 'admin';
  is_active: boolean;
  avatar?: string;
  email_verified_at?: string;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    user: User;
    token: string;
  };
  message?: string;
  error?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
}

// Design Types
export interface Design {
  id: string;
  name: string;
  description?: string;
  file_url: string;
  file_type: string;
  file_size: number;
  thumbnail_url?: string;
  tags: string[];
  status: DesignStatus;
  user_id: string;
  ai_video_url?: string;
  ai_mockup_urls: string[];
  created_at: string;
  updated_at: string;
}

export enum DesignStatus {
  DRAFT = 'draft',
  PROCESSING = 'processing',
  READY = 'ready',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

// Order Types
export interface Order {
  id: string;
  shopify_order_id: string;
  shopify_store_id: string;
  order_number: string;
  customer_email: string;
  customer_name: string;
  shipping_address: ShippingAddress;
  billing_address: BillingAddress;
  line_items: OrderLineItem[];
  total_price: number;
  currency: string;
  payment_status: PaymentStatus;
  fulfillment_status: FulfillmentStatus;
  order_status: OrderStatus;
  notes?: string;
  tags: string[];
  shopify_created_at?: string;
  approved_at?: string;
  created_at: string;
  updated_at: string;
  shop_name?: string;
  shop_domain?: string;
}

export interface OrderLineItem {
  id: string;
  shopify_line_item_id: string;
  product_id: string;
  variant_id: string;
  design_id: string;
  title: string;
  quantity: number;
  price: number;
  sku: string;
  gearment_order_id?: string;
  tracking_number?: string;
  tracking_url?: string;
}

export interface ShippingAddress {
  first_name: string;
  last_name: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  province: string;
  country: string;
  zip: string;
  phone?: string;
}

export interface BillingAddress extends ShippingAddress {}

export enum PaymentStatus {
  PENDING = 'pending',
  AUTHORIZED = 'authorized',
  PARTIALLY_PAID = 'partially_paid',
  PAID = 'paid',
  PARTIALLY_REFUNDED = 'partially_refunded',
  REFUNDED = 'refunded',
  VOIDED = 'voided'
}

export enum FulfillmentStatus {
  UNFULFILLED = 'unfulfilled',
  PARTIAL = 'partial',
  FULFILLED = 'fulfilled'
}

export enum OrderStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

// Shopify Types
export interface ShopifyStore {
  id: string;
  name: string;
  shop_domain: string;
  webhook_verified: boolean;
  is_active: boolean;
  user_id: string;
  store_info?: any;
  last_sync_at?: string;
  created_at: string;
  updated_at: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

// Filter and Search Types
export interface DesignFilters {
  status?: DesignStatus;
  search?: string;
  tags?: string[];
  start_date?: string;
  end_date?: string;
}

export interface OrderFilters {
  status?: OrderStatus;
  payment_status?: PaymentStatus;
  fulfillment_status?: FulfillmentStatus;
  shop_id?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
}

// Dashboard Types
export interface DashboardStats {
  total_designs: number;
  total_orders: number;
  total_revenue: number;
  pending_orders: number;
  active_designs: number;
  connected_stores: number;
}

export interface RecentActivity {
  id: string;
  type: 'design_uploaded' | 'order_received' | 'order_approved' | 'order_shipped' | 'store_connected' | 'payment_received';
  title: string;
  description: string;
  timestamp: string;
  metadata?: any;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
}

// Form Types
export interface FormState {
  isLoading: boolean;
  error?: string;
  success?: string;
}

// Navigation Types
export interface NavItem {
  title: string;
  href: string;
  icon: string;
  badge?: string | number;
  children?: NavItem[];
}
