{"name": "@repo/types", "version": "1.0.0", "description": "Shared TypeScript types for Design Fulfillment System", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {}, "devDependencies": {"typescript": "^5.3.3"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}