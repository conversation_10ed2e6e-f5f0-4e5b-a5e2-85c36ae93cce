{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": false, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@repo/types": ["./packages/types/src"], "@repo/config": ["./packages/config/src"], "@repo/ui": ["./packages/ui/src"]}}, "include": [], "exclude": ["node_modules", "dist", ".next", "coverage"]}